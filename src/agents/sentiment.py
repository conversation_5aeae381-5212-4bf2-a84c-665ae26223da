"""
SentimentAgent - Social sentiment analysis and monitoring.

This agent analyzes social sentiment from various sources including:
- Twitter/X mentions and discussions
- Telegram group activity
- Discord server activity
- Reddit discussions
- News articles and blogs
- GitHub activity (for technical projects)
- Social media influencer mentions
"""

import asyncio
import logging
import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
from collections import Counter

import aiohttp
import polars as pl

from ..core.config import settings
from ..core.database import DatabaseManager
from ..core.cache import CacheManager


class SentimentScore(Enum):
    """Sentiment score categories."""
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"


class SocialPlatform(Enum):
    """Social media platforms."""
    TWITTER = "twitter"
    TELEGRAM = "telegram"
    DISCORD = "discord"
    REDDIT = "reddit"
    YOUTUBE = "youtube"
    MEDIUM = "medium"
    GITHUB = "github"
    NEWS = "news"


@dataclass
class SocialPost:
    """Individual social media post or mention."""
    platform: SocialPlatform
    post_id: str
    author: str
    content: str
    sentiment_score: float  # -1 to 1
    sentiment_category: SentimentScore
    engagement: Dict[str, int]  # likes, shares, comments, etc.
    timestamp: datetime
    url: Optional[str]
    is_verified: bool
    follower_count: Optional[int]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['platform'] = self.platform.value
        result['sentiment_category'] = self.sentiment_category.value
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class SentimentMetrics:
    """Aggregated sentiment metrics."""
    token_address: str
    chain_id: int
    timeframe: str  # 1h, 24h, 7d, 30d
    
    # Overall sentiment
    overall_sentiment: SentimentScore
    sentiment_score: float  # -1 to 1
    confidence: float  # 0-100
    
    # Volume metrics
    total_mentions: int
    unique_authors: int
    verified_mentions: int
    
    # Platform breakdown
    platform_distribution: Dict[str, int]
    platform_sentiment: Dict[str, float]
    
    # Engagement metrics
    total_engagement: int
    avg_engagement_per_post: float
    viral_posts: int  # Posts with high engagement
    
    # Trending analysis
    mention_trend: float  # % change in mentions
    sentiment_trend: float  # % change in sentiment
    is_trending: bool
    trending_keywords: List[str]
    
    # Influencer analysis
    influencer_mentions: int
    influencer_sentiment: float
    top_influencers: List[Dict[str, Any]]
    
    # Time analysis
    sentiment_by_hour: Dict[int, float]
    peak_activity_hours: List[int]
    
    # Risk indicators
    fud_score: float  # Fear, Uncertainty, Doubt score
    hype_score: float  # Excessive positive sentiment score
    manipulation_risk: float  # Risk of sentiment manipulation
    
    # Analysis metadata
    data_quality: float  # 0-100, quality of data sources
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['overall_sentiment'] = self.overall_sentiment.value
        result['last_updated'] = self.last_updated.isoformat()
        return result


@dataclass
class TrendingTopic:
    """Trending topic or keyword."""
    keyword: str
    mention_count: int
    sentiment_score: float
    growth_rate: float  # % increase in mentions
    platforms: List[SocialPlatform]
    sample_posts: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['platforms'] = [p.value for p in self.platforms]
        return result


class SentimentAgent:
    """Agent for social sentiment analysis and monitoring."""
    
    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        session: Optional[aiohttp.ClientSession] = None
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.session = session
        self.logger = logging.getLogger(__name__)
        
        # API configurations
        self.api_configs = {
            'twitter': {
                'base_url': 'https://api.twitter.com/2',
                'bearer_token': settings.TWITTER_BEARER_TOKEN,
                'rate_limit': 300  # requests per 15 minutes
            },
            'reddit': {
                'base_url': 'https://oauth.reddit.com',
                'client_id': settings.REDDIT_CLIENT_ID,
                'client_secret': settings.REDDIT_CLIENT_SECRET,
                'rate_limit': 60  # requests per minute
            }
        }
        
        # Sentiment analysis keywords
        self.positive_keywords = [
            'moon', 'bullish', 'pump', 'rocket', 'gem', 'diamond', 'hold',
            'buy', 'accumulate', 'undervalued', 'potential', 'breakthrough',
            'adoption', 'partnership', 'listing', 'launch', 'success'
        ]
        
        self.negative_keywords = [
            'dump', 'crash', 'rug', 'scam', 'dead', 'fail', 'bearish',
            'sell', 'exit', 'warning', 'risk', 'loss', 'hack', 'exploit'
        ]
        
        # Influencer verification thresholds
        self.influencer_thresholds = {
            'twitter': {'followers': 10000, 'verified': True},
            'telegram': {'members': 5000},
            'youtube': {'subscribers': 50000},
            'reddit': {'karma': 10000}
        }
    
    async def analyze_sentiment(
        self,
        token_address: str,
        chain_id: int = None,
        timeframe: str = "24h",
        force_refresh: bool = False,
        # Alternative parameters for backward compatibility
        symbol: str = None,
        **kwargs
    ) -> SentimentMetrics:
        """
        Analyze social sentiment for a token.
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            timeframe: Analysis timeframe (1h, 24h, 7d, 30d)
            force_refresh: Force refresh cached data
            symbol: Token symbol (optional for compatibility)
            
        Returns:
            SentimentMetrics with comprehensive sentiment analysis
        """
        # Handle backward compatibility and optional parameters
        if chain_id is None:
            chain_id = 1  # Default to Ethereum mainnet
        
        cache_key = f"sentiment_metrics:{chain_id}:{token_address.lower()}:{timeframe}"
        
        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                self.logger.info(f"Using cached sentiment analysis for {token_address}")
                return self._deserialize_sentiment_metrics(cached)
        
        self.logger.info(f"Analyzing sentiment for {token_address} over {timeframe}")
        
        try:
            # Get token information for search queries
            token_info = await self._get_token_info(token_address, chain_id)
            search_terms = self._build_search_terms(token_info)
            
            # Collect social data from multiple platforms
            tasks = [
                self._collect_twitter_data(search_terms, timeframe),
                self._collect_reddit_data(search_terms, timeframe),
                self._collect_telegram_data(search_terms, timeframe),
                self._collect_news_data(search_terms, timeframe)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            all_posts = []
            for result in results:
                if not isinstance(result, Exception) and result:
                    all_posts.extend(result)
            
            if not all_posts:
                self.logger.warning(f"No social data found for {token_address}")
                return self._create_empty_sentiment_metrics(token_address, chain_id, timeframe)
            
            # Analyze sentiment
            sentiment_metrics = await self._analyze_posts(
                all_posts, token_address, chain_id, timeframe
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                sentiment_metrics.to_dict(),
                ttl=1800  # Cache for 30 minutes
            )
            
            # Store in database
            await self._store_sentiment_metrics(sentiment_metrics)
            
            self.logger.info(f"Sentiment analysis completed for {token_address}")
            return sentiment_metrics
            
        except Exception as e:
            self.logger.error(f"Sentiment analysis failed for {token_address}: {e}")
            return self._create_empty_sentiment_metrics(token_address, chain_id, timeframe)
    
    async def _get_token_info(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get token information for building search terms."""
        try:
            results = await self.db_manager.fetch_all("""
                SELECT name, symbol, description, website
                FROM token_info
                WHERE address = ? AND chain_id = ?
            """, (token_address.lower(), chain_id))
            
            if results:
                return dict(results[0])
            
            return {
                'name': '',
                'symbol': '',
                'description': '',
                'website': ''
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get token info: {e}")
            return {'name': '', 'symbol': '', 'description': '', 'website': ''}
    
    def _build_search_terms(self, token_info: Dict[str, Any]) -> List[str]:
        """Build search terms from token information."""
        terms = []
        
        # Add token symbol and name
        if token_info.get('symbol'):
            terms.append(f"${token_info['symbol']}")
            terms.append(token_info['symbol'])
        
        if token_info.get('name') and token_info['name'] != token_info.get('symbol'):
            terms.append(token_info['name'])
        
        # Add contract address (truncated for social media)
        if len(terms) == 0:  # Fallback if no name/symbol
            terms.append(f"0x{token_info.get('address', '')[:8]}")
        
        return terms[:3]  # Limit to top 3 terms
    
    async def _collect_twitter_data(self, search_terms: List[str], timeframe: str) -> List[SocialPost]:
        """Collect data from Twitter/X."""
        posts = []
        
        try:
            if not self.api_configs['twitter']['bearer_token']:
                self.logger.warning("Twitter API token not configured")
                return posts
            
            # Convert timeframe to hours
            hours = self._timeframe_to_hours(timeframe)
            since_time = datetime.utcnow() - timedelta(hours=hours)
            
            for term in search_terms:
                try:
                    if self.session:
                        twitter_posts = await self._fetch_twitter_posts(self.session, term, since_time)
                        posts.extend(twitter_posts)
                    else:
                        async with aiohttp.ClientSession() as session:
                            twitter_posts = await self._fetch_twitter_posts(session, term, since_time)
                            posts.extend(twitter_posts)
                    
                    # Rate limiting
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Failed to fetch Twitter data for {term}: {e}")
            
            return posts
            
        except Exception as e:
            self.logger.error(f"Twitter data collection failed: {e}")
            return []
    
    async def _fetch_twitter_posts(
        self, 
        session: aiohttp.ClientSession, 
        search_term: str, 
        since_time: datetime
    ) -> List[SocialPost]:
        """Fetch posts from Twitter API."""
        try:
            # For now, return mock data since Twitter API requires authentication
            return self._generate_mock_twitter_posts(search_term, since_time)
            
        except Exception as e:
            self.logger.error(f"Twitter API error: {e}")
            return []
    
    def _generate_mock_twitter_posts(self, search_term: str, since_time: datetime) -> List[SocialPost]:
        """Generate mock Twitter posts for testing."""
        posts = []
        
        # Sample tweet content
        sample_tweets = [
            f"{search_term} looking bullish! Great project with solid fundamentals 🚀",
            f"Just bought more {search_term}. This gem is undervalued",
            f"{search_term} pump incoming? Chart looks good",
            f"Warning about {search_term} - do your own research",
            f"{search_term} to the moon! Diamond hands 💎",
            f"Selling my {search_term} bags, too risky for me",
            f"New partnership announced for {search_term}! Huge news",
            f"{search_term} tokenomics look suspicious, be careful"
        ]
        
        for i, content in enumerate(sample_tweets):
            sentiment_score = self._analyze_text_sentiment(content)
            timestamp = since_time + timedelta(hours=i)
            
            posts.append(SocialPost(
                platform=SocialPlatform.TWITTER,
                post_id=f"twitter_{i}",
                author=f"user_{i}",
                content=content,
                sentiment_score=sentiment_score,
                sentiment_category=self._score_to_category(sentiment_score),
                engagement={'likes': 10 + i * 5, 'retweets': 2 + i, 'replies': 1 + i},
                timestamp=timestamp,
                url=f"https://twitter.com/user_{i}/status/{1000+i}",
                is_verified=i % 3 == 0,  # Every 3rd user is verified
                follower_count=1000 + i * 500
            ))
        
        return posts
    
    async def _collect_reddit_data(self, search_terms: List[str], timeframe: str) -> List[SocialPost]:
        """Collect data from Reddit."""
        try:
            # For now, return mock data
            return self._generate_mock_reddit_posts(search_terms[0] if search_terms else "token", timeframe)
        except Exception as e:
            self.logger.error(f"Reddit data collection failed: {e}")
            return []
    
    def _generate_mock_reddit_posts(self, search_term: str, timeframe: str) -> List[SocialPost]:
        """Generate mock Reddit posts for testing."""
        posts = []
        hours = self._timeframe_to_hours(timeframe)
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        sample_posts = [
            f"DD: Why {search_term} is the next 100x gem",
            f"{search_term} technical analysis - breakout incoming",
            f"Thoughts on {search_term}? Seems like a solid project",
            f"Warning: {search_term} might be a rug pull",
            f"{search_term} fundamentals are strong, holding long term",
            f"Sold all my {search_term}, market is too volatile"
        ]
        
        for i, content in enumerate(sample_posts):
            sentiment_score = self._analyze_text_sentiment(content)
            timestamp = since_time + timedelta(hours=i * 2)
            
            posts.append(SocialPost(
                platform=SocialPlatform.REDDIT,
                post_id=f"reddit_{i}",
                author=f"redditor_{i}",
                content=content,
                sentiment_score=sentiment_score,
                sentiment_category=self._score_to_category(sentiment_score),
                engagement={'upvotes': 50 + i * 10, 'comments': 5 + i * 2},
                timestamp=timestamp,
                url=f"https://reddit.com/r/crypto/post_{i}",
                is_verified=False,
                follower_count=None
            ))
        
        return posts
    
    async def _collect_telegram_data(self, search_terms: List[str], timeframe: str) -> List[SocialPost]:
        """Collect data from Telegram."""
        try:
            # Telegram data collection would require specific APIs or web scraping
            # For now, return mock data
            return self._generate_mock_telegram_posts(search_terms[0] if search_terms else "token", timeframe)
        except Exception as e:
            self.logger.error(f"Telegram data collection failed: {e}")
            return []
    
    def _generate_mock_telegram_posts(self, search_term: str, timeframe: str) -> List[SocialPost]:
        """Generate mock Telegram posts for testing."""
        posts = []
        hours = self._timeframe_to_hours(timeframe)
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        sample_messages = [
            f"{search_term} holders unite! 💪",
            f"When {search_term} moon? 🌙",
            f"Just aped into {search_term}, let's go!",
            f"{search_term} chart looking sus... 📉",
            f"Dev team delivered again! {search_term} 🚀",
            f"Anyone else concerned about {search_term} tokenomics?"
        ]
        
        for i, content in enumerate(sample_messages):
            sentiment_score = self._analyze_text_sentiment(content)
            timestamp = since_time + timedelta(minutes=i * 30)
            
            posts.append(SocialPost(
                platform=SocialPlatform.TELEGRAM,
                post_id=f"telegram_{i}",
                author=f"telegram_user_{i}",
                content=content,
                sentiment_score=sentiment_score,
                sentiment_category=self._score_to_category(sentiment_score),
                engagement={'views': 100 + i * 20},
                timestamp=timestamp,
                url=None,
                is_verified=False,
                follower_count=None
            ))
        
        return posts
    
    async def _collect_news_data(self, search_terms: List[str], timeframe: str) -> List[SocialPost]:
        """Collect data from news sources."""
        try:
            # News data collection would use news APIs
            # For now, return mock data
            return self._generate_mock_news_posts(search_terms[0] if search_terms else "token", timeframe)
        except Exception as e:
            self.logger.error(f"News data collection failed: {e}")
            return []
    
    def _generate_mock_news_posts(self, search_term: str, timeframe: str) -> List[SocialPost]:
        """Generate mock news posts for testing."""
        posts = []
        hours = self._timeframe_to_hours(timeframe)
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        sample_articles = [
            f"{search_term} announces major partnership with industry leader",
            f"Market analysis: {search_term} shows strong technical indicators",
            f"Regulatory concerns may impact {search_term} adoption",
            f"{search_term} development team releases major update",
            f"Institutional investors show interest in {search_term}",
            f"Security researchers identify potential issues with {search_term}"
        ]
        
        for i, content in enumerate(sample_articles):
            sentiment_score = self._analyze_text_sentiment(content)
            timestamp = since_time + timedelta(hours=i * 4)
            
            posts.append(SocialPost(
                platform=SocialPlatform.NEWS,
                post_id=f"news_{i}",
                author=f"News Source {i+1}",
                content=content,
                sentiment_score=sentiment_score,
                sentiment_category=self._score_to_category(sentiment_score),
                engagement={'shares': 20 + i * 5, 'comments': 10 + i * 2},
                timestamp=timestamp,
                url=f"https://cryptonews.com/article_{i}",
                is_verified=True,
                follower_count=10000 + i * 5000
            ))
        
        return posts
    
    def _timeframe_to_hours(self, timeframe: str) -> int:
        """Convert timeframe string to hours."""
        timeframe_map = {
            '1h': 1,
            '24h': 24,
            '7d': 168,
            '30d': 720
        }
        return timeframe_map.get(timeframe, 24)
    
    def _analyze_text_sentiment(self, text: str) -> float:
        """Analyze sentiment of text content."""
        try:
            text_lower = text.lower()
            
            # Count positive and negative keywords
            positive_count = sum(1 for keyword in self.positive_keywords if keyword in text_lower)
            negative_count = sum(1 for keyword in self.negative_keywords if keyword in text_lower)
            
            # Simple sentiment scoring
            if positive_count == 0 and negative_count == 0:
                return 0.0  # Neutral
            
            total_keywords = positive_count + negative_count
            sentiment = (positive_count - negative_count) / max(total_keywords, 1)
            
            # Add emoji sentiment
            emoji_sentiment = self._analyze_emoji_sentiment(text)
            
            # Combine text and emoji sentiment
            final_sentiment = (sentiment * 0.7) + (emoji_sentiment * 0.3)
            
            # Clamp to [-1, 1]
            return max(-1.0, min(1.0, final_sentiment))
            
        except Exception as e:
            self.logger.error(f"Failed to analyze text sentiment: {e}")
            return 0.0
    
    def _analyze_emoji_sentiment(self, text: str) -> float:
        """Analyze sentiment based on emojis."""
        positive_emojis = ['🚀', '🌙', '💎', '📈', '💪', '🔥', '✅', '👍', '❤️', '😍']
        negative_emojis = ['📉', '💸', '😢', '😭', '❌', '👎', '💀', '🐻', '⚠️', '🚨']
        
        positive_count = sum(text.count(emoji) for emoji in positive_emojis)
        negative_count = sum(text.count(emoji) for emoji in negative_emojis)
        
        if positive_count == 0 and negative_count == 0:
            return 0.0
        
        total = positive_count + negative_count
        return (positive_count - negative_count) / total
    
    def _score_to_category(self, score: float) -> SentimentScore:
        """Convert numerical sentiment score to category."""
        if score <= -0.6:
            return SentimentScore.VERY_NEGATIVE
        elif score <= -0.2:
            return SentimentScore.NEGATIVE
        elif score <= 0.2:
            return SentimentScore.NEUTRAL
        elif score <= 0.6:
            return SentimentScore.POSITIVE
        else:
            return SentimentScore.VERY_POSITIVE
    
    async def _analyze_posts(
        self, 
        posts: List[SocialPost], 
        token_address: str, 
        chain_id: int, 
        timeframe: str
    ) -> SentimentMetrics:
        """Analyze collected posts to generate sentiment metrics."""
        try:
            # Basic metrics
            total_mentions = len(posts)
            unique_authors = len(set(post.author for post in posts))
            verified_mentions = sum(1 for post in posts if post.is_verified)
            
            # Platform distribution
            platform_counts = Counter(post.platform.value for post in posts)
            platform_distribution = dict(platform_counts)
            
            # Platform sentiment
            platform_sentiment = {}
            for platform in platform_counts:
                platform_posts = [p for p in posts if p.platform.value == platform]
                if platform_posts:
                    avg_sentiment = statistics.mean(p.sentiment_score for p in platform_posts)
                    platform_sentiment[platform] = avg_sentiment
            
            # Overall sentiment
            if posts:
                sentiment_scores = [post.sentiment_score for post in posts]
                overall_sentiment_score = statistics.mean(sentiment_scores)
                sentiment_std = statistics.stdev(sentiment_scores) if len(sentiment_scores) > 1 else 0
                confidence = min(100.0, max(0.0, (1 - sentiment_std) * 100))
            else:
                overall_sentiment_score = 0.0
                confidence = 0.0
            
            overall_sentiment = self._score_to_category(overall_sentiment_score)
            
            # Engagement metrics
            total_engagement = 0
            viral_posts = 0
            for post in posts:
                post_engagement = sum(post.engagement.values())
                total_engagement += post_engagement
                
                # Consider viral if engagement > 100
                if post_engagement > 100:
                    viral_posts += 1
            
            avg_engagement_per_post = total_engagement / total_mentions if total_mentions > 0 else 0
            
            # Trending analysis
            mention_trend, sentiment_trend, is_trending = await self._analyze_trends(
                token_address, chain_id, timeframe, total_mentions, overall_sentiment_score
            )
            
            # Trending keywords
            trending_keywords = self._extract_trending_keywords(posts)
            
            # Influencer analysis
            influencer_posts = [p for p in posts if self._is_influencer(p)]
            influencer_mentions = len(influencer_posts)
            influencer_sentiment = statistics.mean(
                [p.sentiment_score for p in influencer_posts]
            ) if influencer_posts else 0.0
            
            top_influencers = self._get_top_influencers(influencer_posts)
            
            # Time analysis
            sentiment_by_hour, peak_activity_hours = self._analyze_time_patterns(posts)
            
            # Risk indicators
            fud_score = self._calculate_fud_score(posts)
            hype_score = self._calculate_hype_score(posts)
            manipulation_risk = self._calculate_manipulation_risk(posts)
            
            # Data quality
            data_quality = self._assess_data_quality(posts)
            
            return SentimentMetrics(
                token_address=token_address.lower(),
                chain_id=chain_id,
                timeframe=timeframe,
                overall_sentiment=overall_sentiment,
                sentiment_score=overall_sentiment_score,
                confidence=confidence,
                total_mentions=total_mentions,
                unique_authors=unique_authors,
                verified_mentions=verified_mentions,
                platform_distribution=platform_distribution,
                platform_sentiment=platform_sentiment,
                total_engagement=total_engagement,
                avg_engagement_per_post=avg_engagement_per_post,
                viral_posts=viral_posts,
                mention_trend=mention_trend,
                sentiment_trend=sentiment_trend,
                is_trending=is_trending,
                trending_keywords=trending_keywords,
                influencer_mentions=influencer_mentions,
                influencer_sentiment=influencer_sentiment,
                top_influencers=top_influencers,
                sentiment_by_hour=sentiment_by_hour,
                peak_activity_hours=peak_activity_hours,
                fud_score=fud_score,
                hype_score=hype_score,
                manipulation_risk=manipulation_risk,
                data_quality=data_quality,
                last_updated=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze posts: {e}")
            return self._create_empty_sentiment_metrics(token_address, chain_id, timeframe)
    
    async def _analyze_trends(
        self, 
        token_address: str, 
        chain_id: int, 
        timeframe: str, 
        current_mentions: int, 
        current_sentiment: float
    ) -> Tuple[float, float, bool]:
        """Analyze mention and sentiment trends."""
        try:
            # Get historical data
            hours_ago = self._timeframe_to_hours(timeframe)
            past_timestamp = datetime.utcnow() - timedelta(hours=hours_ago * 2)
            
            results = await self.db_manager.fetch_all("""
                SELECT total_mentions, sentiment_score
                FROM sentiment_metrics
                WHERE token_address = ? AND chain_id = ? AND timeframe = ?
                AND last_updated > ?
                ORDER BY last_updated DESC
                LIMIT 5
            """, (token_address.lower(), chain_id, timeframe, past_timestamp.isoformat()))
            
            if not results:
                return 0.0, 0.0, False
            
            # Calculate trends
            past_mentions = [row['total_mentions'] for row in results]
            past_sentiments = [row['sentiment_score'] for row in results]
            
            avg_past_mentions = statistics.mean(past_mentions)
            avg_past_sentiment = statistics.mean(past_sentiments)
            
            mention_trend = ((current_mentions - avg_past_mentions) / avg_past_mentions * 100) if avg_past_mentions > 0 else 0
            sentiment_trend = ((current_sentiment - avg_past_sentiment) / abs(avg_past_sentiment) * 100) if avg_past_sentiment != 0 else 0
            
            # Determine if trending (significant increase in mentions)
            is_trending = mention_trend > 50 and current_mentions > 10
            
            return mention_trend, sentiment_trend, is_trending
            
        except Exception as e:
            self.logger.error(f"Failed to analyze trends: {e}")
            return 0.0, 0.0, False
    
    def _extract_trending_keywords(self, posts: List[SocialPost]) -> List[str]:
        """Extract trending keywords from posts."""
        try:
            # Extract words from all posts
            all_words = []
            for post in posts:
                # Simple word extraction (excluding common words)
                words = re.findall(r'\b[a-zA-Z]{3,}\b', post.content.lower())
                all_words.extend(words)
            
            # Filter common words
            common_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
                'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 
                'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
                'did', 'what', 'when', 'where', 'why', 'this', 'that', 'with', 'have',
                'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time',
                'very', 'well', 'come', 'could', 'just', 'like', 'long', 'make', 'many',
                'over', 'said', 'them', 'way', 'will', 'would'
            }
            
            filtered_words = [word for word in all_words if word not in common_words and len(word) > 3]
            
            # Count frequency and return top keywords
            word_counts = Counter(filtered_words)
            return [word for word, count in word_counts.most_common(10)]
            
        except Exception as e:
            self.logger.error(f"Failed to extract keywords: {e}")
            return []
    
    def _is_influencer(self, post: SocialPost) -> bool:
        """Determine if a post is from an influencer."""
        platform_thresholds = self.influencer_thresholds.get(post.platform.value, {})
        
        # Check verification status
        if platform_thresholds.get('verified') and post.is_verified:
            return True
        
        # Check follower count
        min_followers = platform_thresholds.get('followers', 0)
        if post.follower_count and post.follower_count >= min_followers:
            return True
        
        return False
    
    def _get_top_influencers(self, influencer_posts: List[SocialPost]) -> List[Dict[str, Any]]:
        """Get top influencers by engagement."""
        try:
            influencer_data = {}
            
            for post in influencer_posts:
                if post.author not in influencer_data:
                    influencer_data[post.author] = {
                        'author': post.author,
                        'platform': post.platform.value,
                        'follower_count': post.follower_count or 0,
                        'total_engagement': 0,
                        'post_count': 0,
                        'avg_sentiment': 0,
                        'is_verified': post.is_verified
                    }
                
                influencer_data[post.author]['total_engagement'] += sum(post.engagement.values())
                influencer_data[post.author]['post_count'] += 1
                influencer_data[post.author]['avg_sentiment'] = (
                    (influencer_data[post.author]['avg_sentiment'] * (influencer_data[post.author]['post_count'] - 1) + post.sentiment_score) /
                    influencer_data[post.author]['post_count']
                )
            
            # Sort by total engagement and return top 5
            sorted_influencers = sorted(
                influencer_data.values(),
                key=lambda x: x['total_engagement'],
                reverse=True
            )
            
            return sorted_influencers[:5]
            
        except Exception as e:
            self.logger.error(f"Failed to get top influencers: {e}")
            return []
    
    def _analyze_time_patterns(self, posts: List[SocialPost]) -> Tuple[Dict[int, float], List[int]]:
        """Analyze sentiment and activity patterns by hour."""
        try:
            # Group posts by hour
            hourly_data = {}
            for hour in range(24):
                hourly_data[hour] = {'posts': [], 'count': 0}
            
            for post in posts:
                hour = post.timestamp.hour
                hourly_data[hour]['posts'].append(post)
                hourly_data[hour]['count'] += 1
            
            # Calculate sentiment by hour
            sentiment_by_hour = {}
            activity_counts = {}
            
            for hour, data in hourly_data.items():
                if data['posts']:
                    sentiment_by_hour[hour] = statistics.mean(p.sentiment_score for p in data['posts'])
                else:
                    sentiment_by_hour[hour] = 0.0
                
                activity_counts[hour] = data['count']
            
            # Find peak activity hours (top 3 hours with most posts)
            peak_hours = sorted(activity_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            peak_activity_hours = [hour for hour, count in peak_hours if count > 0]
            
            return sentiment_by_hour, peak_activity_hours
            
        except Exception as e:
            self.logger.error(f"Failed to analyze time patterns: {e}")
            return {}, []
    
    def _calculate_fud_score(self, posts: List[SocialPost]) -> float:
        """Calculate Fear, Uncertainty, Doubt score."""
        try:
            fud_keywords = ['scam', 'rug', 'dump', 'crash', 'warning', 'risk', 'dangerous', 'avoid']
            
            fud_posts = []
            for post in posts:
                content_lower = post.content.lower()
                fud_mentions = sum(1 for keyword in fud_keywords if keyword in content_lower)
                if fud_mentions > 0:
                    fud_posts.append(post)
            
            if not posts:
                return 0.0
            
            fud_ratio = len(fud_posts) / len(posts)
            
            # Weight by engagement (viral FUD is more concerning)
            if fud_posts:
                avg_fud_engagement = statistics.mean(sum(p.engagement.values()) for p in fud_posts)
                avg_total_engagement = statistics.mean(sum(p.engagement.values()) for p in posts)
                engagement_multiplier = avg_fud_engagement / avg_total_engagement if avg_total_engagement > 0 else 1
            else:
                engagement_multiplier = 1
            
            return min(100.0, fud_ratio * 100 * engagement_multiplier)
            
        except Exception as e:
            self.logger.error(f"Failed to calculate FUD score: {e}")
            return 0.0
    
    def _calculate_hype_score(self, posts: List[SocialPost]) -> float:
        """Calculate excessive hype score."""
        try:
            hype_keywords = ['moon', 'rocket', 'lambo', '100x', '1000x', 'diamond', 'ape']
            
            hype_posts = []
            very_positive_posts = []
            
            for post in posts:
                content_lower = post.content.lower()
                hype_mentions = sum(1 for keyword in hype_keywords if keyword in content_lower)
                
                if hype_mentions > 0:
                    hype_posts.append(post)
                
                if post.sentiment_score > 0.8:
                    very_positive_posts.append(post)
            
            if not posts:
                return 0.0
            
            hype_ratio = len(hype_posts) / len(posts)
            extreme_positive_ratio = len(very_positive_posts) / len(posts)
            
            # Combine ratios
            hype_score = (hype_ratio * 0.6 + extreme_positive_ratio * 0.4) * 100
            
            return min(100.0, hype_score)
            
        except Exception as e:
            self.logger.error(f"Failed to calculate hype score: {e}")
            return 0.0
    
    def _calculate_manipulation_risk(self, posts: List[SocialPost]) -> float:
        """Calculate risk of sentiment manipulation."""
        try:
            if not posts:
                return 0.0
            
            # Check for suspicious patterns
            risk_factors = []
            
            # 1. Too many posts from new/low-follower accounts
            low_follower_posts = [p for p in posts if p.follower_count and p.follower_count < 100]
            if len(low_follower_posts) / len(posts) > 0.6:
                risk_factors.append(30)
            
            # 2. Unusually uniform sentiment
            sentiment_scores = [p.sentiment_score for p in posts]
            if len(sentiment_scores) > 5:
                sentiment_std = statistics.stdev(sentiment_scores)
                if sentiment_std < 0.1:  # Very uniform sentiment
                    risk_factors.append(25)
            
            # 3. High volume of posts in short time
            if len(posts) > 50:
                time_span = (max(p.timestamp for p in posts) - min(p.timestamp for p in posts)).total_seconds() / 3600
                if time_span < 1 and len(posts) > 20:  # 20+ posts in 1 hour
                    risk_factors.append(20)
            
            # 4. Repetitive content
            unique_content = set(p.content[:50] for p in posts)  # First 50 chars
            if len(unique_content) / len(posts) < 0.7:  # Less than 70% unique content
                risk_factors.append(25)
            
            return min(100.0, sum(risk_factors))
            
        except Exception as e:
            self.logger.error(f"Failed to calculate manipulation risk: {e}")
            return 0.0
    
    def _assess_data_quality(self, posts: List[SocialPost]) -> float:
        """Assess quality of collected data."""
        try:
            if not posts:
                return 0.0
            
            quality_score = 100.0
            
            # Penalize for low data volume
            if len(posts) < 10:
                quality_score -= 30
            elif len(posts) < 5:
                quality_score -= 50
            
            # Reward for verified sources
            verified_ratio = sum(1 for p in posts if p.is_verified) / len(posts)
            quality_score += verified_ratio * 20
            
            # Reward for platform diversity
            platforms = set(p.platform for p in posts)
            if len(platforms) >= 3:
                quality_score += 15
            elif len(platforms) >= 2:
                quality_score += 10
            
            # Penalize for manipulation risk
            manipulation_risk = self._calculate_manipulation_risk(posts)
            quality_score -= manipulation_risk * 0.3
            
            return max(0.0, min(100.0, quality_score))
            
        except Exception as e:
            self.logger.error(f"Failed to assess data quality: {e}")
            return 50.0
    
    def _create_empty_sentiment_metrics(
        self, 
        token_address: str, 
        chain_id: int, 
        timeframe: str
    ) -> SentimentMetrics:
        """Create empty sentiment metrics when no data is available."""
        return SentimentMetrics(
            token_address=token_address.lower(),
            chain_id=chain_id,
            timeframe=timeframe,
            overall_sentiment=SentimentScore.NEUTRAL,
            sentiment_score=0.0,
            confidence=0.0,
            total_mentions=0,
            unique_authors=0,
            verified_mentions=0,
            platform_distribution={},
            platform_sentiment={},
            total_engagement=0,
            avg_engagement_per_post=0.0,
            viral_posts=0,
            mention_trend=0.0,
            sentiment_trend=0.0,
            is_trending=False,
            trending_keywords=[],
            influencer_mentions=0,
            influencer_sentiment=0.0,
            top_influencers=[],
            sentiment_by_hour={},
            peak_activity_hours=[],
            fud_score=0.0,
            hype_score=0.0,
            manipulation_risk=0.0,
            data_quality=0.0,
            last_updated=datetime.utcnow()
        )
    
    def _deserialize_sentiment_metrics(self, data: Dict[str, Any]) -> SentimentMetrics:
        """Deserialize cached sentiment metrics."""
        data['overall_sentiment'] = SentimentScore(data['overall_sentiment'])
        data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return SentimentMetrics(**data)
    
    async def _store_sentiment_metrics(self, metrics: SentimentMetrics) -> None:
        """Store sentiment metrics in database."""
        try:
            await self.db_manager.execute("""
                INSERT OR REPLACE INTO sentiment_metrics (
                    token_address, chain_id, timeframe, overall_sentiment,
                    sentiment_score, confidence, total_mentions, unique_authors,
                    verified_mentions, platform_distribution, platform_sentiment,
                    total_engagement, avg_engagement_per_post, viral_posts,
                    mention_trend, sentiment_trend, is_trending, trending_keywords,
                    influencer_mentions, influencer_sentiment, top_influencers,
                    sentiment_by_hour, peak_activity_hours, fud_score, hype_score,
                    manipulation_risk, data_quality, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics.token_address,
                metrics.chain_id,
                metrics.timeframe,
                metrics.overall_sentiment.value,
                metrics.sentiment_score,
                metrics.confidence,
                metrics.total_mentions,
                metrics.unique_authors,
                metrics.verified_mentions,
                str(metrics.platform_distribution),
                str(metrics.platform_sentiment),
                metrics.total_engagement,
                metrics.avg_engagement_per_post,
                metrics.viral_posts,
                metrics.mention_trend,
                metrics.sentiment_trend,
                metrics.is_trending,
                '\n'.join(metrics.trending_keywords),
                metrics.influencer_mentions,
                metrics.influencer_sentiment,
                str(metrics.top_influencers),
                str(metrics.sentiment_by_hour),
                str(metrics.peak_activity_hours),
                metrics.fud_score,
                metrics.hype_score,
                metrics.manipulation_risk,
                metrics.data_quality,
                metrics.last_updated.isoformat()
            ))
            
            self.logger.info(f"Stored sentiment metrics for {metrics.token_address}")
            
        except Exception as e:
            self.logger.error(f"Failed to store sentiment metrics: {e}")
    
    async def get_trending_tokens(self, chain_id: Optional[int] = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Get tokens with trending social sentiment."""
        try:
            where_clause = ""
            params = []
            
            if chain_id:
                where_clause = "WHERE chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT token_address, chain_id, overall_sentiment, sentiment_score,
                       total_mentions, mention_trend, is_trending
                FROM sentiment_metrics
                {where_clause}
                AND last_updated > datetime('now', '-1 hour')
                AND is_trending = 1
                ORDER BY mention_trend DESC, total_mentions DESC
                LIMIT ?
            """, params + [limit])
            
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error(f"Failed to get trending tokens: {e}")
            return []
    
    async def get_sentiment_summary(self, chain_id: Optional[int] = None) -> Dict[str, Any]:
        """Get sentiment summary across all tokens."""
        try:
            where_clause = ""
            params = []
            
            if chain_id:
                where_clause = "WHERE chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT 
                    COUNT(*) as total_tokens,
                    AVG(sentiment_score) as avg_sentiment,
                    SUM(total_mentions) as total_mentions,
                    COUNT(CASE WHEN is_trending = 1 THEN 1 END) as trending_count,
                    AVG(fud_score) as avg_fud_score,
                    AVG(hype_score) as avg_hype_score,
                    AVG(manipulation_risk) as avg_manipulation_risk
                FROM sentiment_metrics
                {where_clause}
                AND last_updated > datetime('now', '-1 hour')
            """, params)
            
            if results:
                summary = dict(results[0])
                summary['generated_at'] = datetime.utcnow().isoformat()
                return summary
            
            return {'error': 'No data available'}
            
        except Exception as e:
            self.logger.error(f"Failed to get sentiment summary: {e}")
            return {'error': str(e)}
