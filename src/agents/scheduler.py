"""
Scheduler Agent for managing automated tasks and periodic operations.
Handles token discovery schedules, analysis updates, and maintenance tasks.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import json
from dataclasses import dataclass
from enum import Enum

import structlog

from ..core.config import get_settings
from ..core.database import DatabaseManager
from ..core.cache import CacheManager


logger = structlog.get_logger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ScheduledTask:
    """Represents a scheduled task."""
    id: str
    name: str
    agent: str
    method: str
    kwargs: Dict[str, Any]
    schedule: str  # cron-like or interval
    priority: TaskPriority
    status: TaskStatus = TaskStatus.PENDING
    next_run: Optional[datetime] = None
    last_run: Optional[datetime] = None
    last_result: Optional[Dict[str, Any]] = None
    error_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    enabled: bool = True


class SchedulerAgent:
    """
    Scheduler agent for managing automated tasks and periodic operations.
    
    Features:
    - Cron-like scheduling
    - Task prioritization
    - Error handling and retries
    - Task monitoring and metrics
    - Dynamic task management
    """
    
    def __init__(self, db_manager: DatabaseManager, cache_manager: CacheManager, 
                 metrics_collector, coordinator=None):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        self.coordinator = coordinator
        
        # Task management
        self.tasks: Dict[str, ScheduledTask] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_queue: List[ScheduledTask] = []
        
        # Scheduler state
        self.running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        self.max_concurrent_tasks = 5
        
        # Default schedules
        self.default_schedules = {
            "token_discovery": "0 */6 * * *",  # Every 6 hours
            "trending_analysis": "0 */1 * * *",  # Every hour
            "market_data_update": "*/15 * * * *",  # Every 15 minutes
            "cache_cleanup": "0 2 * * *",  # Daily at 2 AM
            "database_maintenance": "0 3 * * 0",  # Weekly on Sunday at 3 AM
            "metrics_collection": "*/5 * * * *",  # Every 5 minutes
        }
    
    async def initialize(self) -> None:
        """Initialize the scheduler agent."""
        try:
            self.logger.info("Initializing SchedulerAgent")
            
            # Load existing tasks from database
            await self._load_tasks()
            
            # Create default tasks if they don't exist
            await self._create_default_tasks()
            
            self.logger.info("SchedulerAgent initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize SchedulerAgent: {e}")
            raise
    
    async def shutdown(self) -> None:
        """Shutdown the scheduler agent."""
        try:
            self.logger.info("Shutting down SchedulerAgent")
            
            # Stop scheduler
            await self.stop()
            
            # Cancel running tasks
            for task_id, task in self.running_tasks.items():
                self.logger.info(f"Cancelling task {task_id}")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("SchedulerAgent shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during SchedulerAgent shutdown: {e}")
    
    async def health_check(self) -> bool:
        """Check agent health."""
        try:
            return self.running and len(self.running_tasks) <= self.max_concurrent_tasks
        except Exception as e:
            self.logger.error(f"SchedulerAgent health check failed: {e}")
            return False
    
    # ==================== SCHEDULER CONTROL ====================
    
    async def start(self) -> None:
        """Start the scheduler."""
        try:
            if self.running:
                return
            
            self.running = True
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            
            self.logger.info("Scheduler started")
            
        except Exception as e:
            self.logger.error(f"Failed to start scheduler: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the scheduler."""
        try:
            if not self.running:
                return
            
            self.running = False
            
            if self.scheduler_task:
                self.scheduler_task.cancel()
                try:
                    await self.scheduler_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Scheduler stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping scheduler: {e}")
    
    async def run(self) -> None:
        """Run the scheduler (main entry point)."""
        await self.start()
        
        try:
            # Keep running until shutdown
            while self.running:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
        finally:
            await self.stop()
    
    # ==================== TASK MANAGEMENT ====================
    
    async def add_task(
        self,
        name: str,
        agent: str,
        method: str,
        schedule: str,
        kwargs: Optional[Dict[str, Any]] = None,
        priority: TaskPriority = TaskPriority.MEDIUM,
        enabled: bool = True
    ) -> str:
        """Add a new scheduled task."""
        try:
            task = ScheduledTask(
                id=f"{agent}_{method}_{datetime.utcnow().timestamp()}",
                name=name,
                agent=agent,
                method=method,
                kwargs=kwargs or {},
                schedule=schedule,
                priority=priority,
                enabled=enabled
            )
            
            # Calculate next run time
            task.next_run = self._calculate_next_run(schedule)
            
            # Store task
            self.tasks[task.id] = task
            await self._save_task(task)
            
            self.logger.info(f"Added scheduled task: {name} ({task.id})")
            return task.id
            
        except Exception as e:
            self.logger.error(f"Failed to add task {name}: {e}")
            raise
    
    async def remove_task(self, task_id: str) -> bool:
        """Remove a scheduled task."""
        try:
            if task_id in self.tasks:
                # Cancel if running
                if task_id in self.running_tasks:
                    self.running_tasks[task_id].cancel()
                    del self.running_tasks[task_id]
                
                # Remove from storage
                del self.tasks[task_id]
                await self._delete_task(task_id)
                
                self.logger.info(f"Removed task {task_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to remove task {task_id}: {e}")
            return False
    
    async def enable_task(self, task_id: str) -> bool:
        """Enable a scheduled task."""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = True
            await self._save_task(self.tasks[task_id])
            return True
        return False
    
    async def disable_task(self, task_id: str) -> bool:
        """Disable a scheduled task."""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = False
            await self._save_task(self.tasks[task_id])
            return True
        return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task."""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        return {
            "id": task.id,
            "name": task.name,
            "agent": task.agent,
            "method": task.method,
            "status": task.status.value,
            "enabled": task.enabled,
            "next_run": task.next_run.isoformat() if task.next_run else None,
            "last_run": task.last_run.isoformat() if task.last_run else None,
            "error_count": task.error_count,
            "last_result": task.last_result
        }
    
    async def list_tasks(self) -> List[Dict[str, Any]]:
        """List all scheduled tasks."""
        tasks = []
        for task in self.tasks.values():
            tasks.append({
                "id": task.id,
                "name": task.name,
                "agent": task.agent,
                "method": task.method,
                "schedule": task.schedule,
                "status": task.status.value,
                "enabled": task.enabled,
                "next_run": task.next_run.isoformat() if task.next_run else None,
                "last_run": task.last_run.isoformat() if task.last_run else None,
                "priority": task.priority.value
            })
        return tasks
    
    # ==================== SCHEDULER EXECUTION ====================
    
    async def _scheduler_loop(self) -> None:
        """Main scheduler loop."""
        try:
            while self.running:
                try:
                    # Check for tasks that need to run
                    await self._check_and_execute_tasks()
                    
                    # Clean up completed tasks
                    await self._cleanup_completed_tasks()
                    
                    # Wait before next check
                    await asyncio.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error in scheduler loop: {e}")
                    await asyncio.sleep(60)  # Wait longer on error
                    
        except asyncio.CancelledError:
            self.logger.info("Scheduler loop cancelled")
            raise
        except Exception as e:
            self.logger.error(f"Scheduler loop failed: {e}")
    
    async def _check_and_execute_tasks(self) -> None:
        """Check for tasks that need to run and execute them."""
        current_time = datetime.utcnow()
        tasks_to_run = []
        
        # Find tasks ready to run
        for task in self.tasks.values():
            if (task.enabled and 
                task.status != TaskStatus.RUNNING and
                task.next_run and 
                task.next_run <= current_time):
                tasks_to_run.append(task)
        
        # Sort by priority
        tasks_to_run.sort(key=lambda t: t.priority.value, reverse=True)
        
        # Execute tasks (respecting concurrency limit)
        for task in tasks_to_run:
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                break
            
            await self._execute_task(task)
    
    async def _execute_task(self, task: ScheduledTask) -> None:
        """Execute a scheduled task."""
        try:
            task.status = TaskStatus.RUNNING
            task.last_run = datetime.utcnow()
            
            # Create execution task
            execution_task = asyncio.create_task(
                self._run_task_with_timeout(task)
            )
            self.running_tasks[task.id] = execution_task
            
            self.logger.info(f"Started task execution: {task.name} ({task.id})")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_count += 1
            self.logger.error(f"Failed to start task execution {task.name}: {e}")
    
    async def _run_task_with_timeout(self, task: ScheduledTask) -> None:
        """Run task with timeout and error handling."""
        try:
            # Execute the actual task
            result = await asyncio.wait_for(
                self._execute_agent_method(task),
                timeout=task.timeout_seconds
            )
            
            # Task completed successfully
            task.status = TaskStatus.COMPLETED
            task.last_result = {
                "success": True,
                "result": result,
                "timestamp": datetime.utcnow().isoformat()
            }
            task.error_count = 0  # Reset error count on success
            
            # Calculate next run time
            task.next_run = self._calculate_next_run(task.schedule)
            
            self.logger.info(f"Task completed successfully: {task.name}")
            
        except asyncio.TimeoutError:
            task.status = TaskStatus.FAILED
            task.error_count += 1
            task.last_result = {
                "success": False,
                "error": "Task timeout",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.logger.error(f"Task timed out: {task.name}")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_count += 1
            task.last_result = {
                "success": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.logger.error(f"Task failed: {task.name} - {e}")
            
        finally:
            # Remove from running tasks
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            # Save task state
            await self._save_task(task)
            
            # Handle retries or disable on too many failures
            if task.error_count >= task.max_retries:
                task.enabled = False
                self.logger.warning(f"Disabled task due to repeated failures: {task.name}")
    
    async def _execute_agent_method(self, task: ScheduledTask) -> Any:
        """Execute the agent method for a task."""
        if not self.coordinator:
            raise Exception("Coordinator not available")
        
        # Get the agent
        agent = self.coordinator.agents.get(task.agent)
        if not agent:
            raise Exception(f"Agent {task.agent} not found")
        
        # Get the method
        method = getattr(agent, task.method, None)
        if not method:
            raise Exception(f"Method {task.method} not found on agent {task.agent}")
        
        # Execute the method
        if asyncio.iscoroutinefunction(method):
            return await method(**task.kwargs)
        else:
            return method(**task.kwargs)
    
    async def _cleanup_completed_tasks(self) -> None:
        """Clean up completed task references."""
        completed_tasks = []
        for task_id, execution_task in self.running_tasks.items():
            if execution_task.done():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.running_tasks[task_id]
    
    # ==================== TASK PERSISTENCE ====================
    
    async def _load_tasks(self) -> None:
        """Load tasks from database."""
        try:
            if not self.db_manager:
                return
            
            rows = await self.db_manager.fetch_all("SELECT * FROM scheduled_tasks")
            
            for row in rows:
                task = ScheduledTask(
                    id=row["id"],
                    name=row["name"],
                    agent=row["agent"],
                    method=row["method"],
                    kwargs=json.loads(row["kwargs"]) if row["kwargs"] else {},
                    schedule=row["schedule"],
                    priority=TaskPriority(row["priority"]),
                    status=TaskStatus(row["status"]),
                    next_run=datetime.fromisoformat(row["next_run"]) if row["next_run"] else None,
                    last_run=datetime.fromisoformat(row["last_run"]) if row["last_run"] else None,
                    last_result=json.loads(row["last_result"]) if row["last_result"] else None,
                    error_count=row["error_count"],
                    max_retries=row["max_retries"],
                    timeout_seconds=row["timeout_seconds"],
                    enabled=bool(row["enabled"])
                )
                
                self.tasks[task.id] = task
            
            self.logger.info(f"Loaded {len(self.tasks)} scheduled tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to load tasks: {e}")
    
    async def _save_task(self, task: ScheduledTask) -> None:
        """Save task to database."""
        try:
            if not self.db_manager:
                return
            
            await self.db_manager.execute_query("""
                INSERT OR REPLACE INTO scheduled_tasks (
                    id, name, agent, method, kwargs, schedule, priority, status,
                    next_run, last_run, last_result, error_count, max_retries,
                    timeout_seconds, enabled, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.id,
                task.name,
                task.agent,
                task.method,
                json.dumps(task.kwargs),
                task.schedule,
                task.priority.value,
                task.status.value,
                task.next_run.isoformat() if task.next_run else None,
                task.last_run.isoformat() if task.last_run else None,
                json.dumps(task.last_result) if task.last_result else None,
                task.error_count,
                task.max_retries,
                task.timeout_seconds,
                int(task.enabled),
                datetime.utcnow().isoformat()
            ))
            
        except Exception as e:
            self.logger.error(f"Failed to save task {task.id}: {e}")
    
    async def _delete_task(self, task_id: str) -> None:
        """Delete task from database."""
        try:
            if not self.db_manager:
                return
            
            await self.db_manager.execute_query(
                "DELETE FROM scheduled_tasks WHERE id = ?",
                (task_id,)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to delete task {task_id}: {e}")
    
    async def _create_default_tasks(self) -> None:
        """Create default scheduled tasks."""
        try:
            default_tasks = [
                {
                    "name": "Token Discovery",
                    "agent": "discovery",
                    "method": "discover_trending_tokens",
                    "schedule": self.default_schedules["token_discovery"],
                    "kwargs": {"limit": 50, "time_window": "24h"},
                    "priority": TaskPriority.HIGH
                },
                {
                    "name": "Trending Analysis",
                    "agent": "coordinator",
                    "method": "run_discovery_pipeline",
                    "schedule": self.default_schedules["trending_analysis"],
                    "kwargs": {"sources": ["defillama", "dexscreener"], "limit": 20},
                    "priority": TaskPriority.MEDIUM
                },
                {
                    "name": "Cache Cleanup",
                    "agent": "cache_manager",
                    "method": "cleanup_expired",
                    "schedule": self.default_schedules["cache_cleanup"],
                    "kwargs": {},
                    "priority": TaskPriority.LOW
                }
            ]
            
            for task_config in default_tasks:
                # Check if task already exists
                existing = any(
                    task.name == task_config["name"] 
                    for task in self.tasks.values()
                )
                
                if not existing:
                    await self.add_task(**task_config)
            
        except Exception as e:
            self.logger.error(f"Failed to create default tasks: {e}")
    
    # ==================== SCHEDULE PARSING ====================
    
    def _calculate_next_run(self, schedule: str) -> datetime:
        """Calculate next run time from schedule string."""
        try:
            # Simple interval parsing (e.g., "5m", "1h", "1d")
            if schedule.endswith('m'):
                minutes = int(schedule[:-1])
                return datetime.utcnow() + timedelta(minutes=minutes)
            elif schedule.endswith('h'):
                hours = int(schedule[:-1])
                return datetime.utcnow() + timedelta(hours=hours)
            elif schedule.endswith('d'):
                days = int(schedule[:-1])
                return datetime.utcnow() + timedelta(days=days)
            
            # For cron expressions, use a simple approach
            # In production, would use a proper cron parser
            parts = schedule.split()
            if len(parts) == 5:
                # Simplified cron parsing - just add 1 hour for now
                return datetime.utcnow() + timedelta(hours=1)
            
            # Default: run in 1 hour
            return datetime.utcnow() + timedelta(hours=1)
            
        except Exception as e:
            self.logger.error(f"Error parsing schedule {schedule}: {e}")
            return datetime.utcnow() + timedelta(hours=1)
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get scheduler metrics."""
        try:
            total_tasks = len(self.tasks)
            enabled_tasks = len([t for t in self.tasks.values() if t.enabled])
            running_tasks = len(self.running_tasks)
            failed_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED])
            
            return {
                "total_tasks": total_tasks,
                "enabled_tasks": enabled_tasks,
                "running_tasks": running_tasks,
                "failed_tasks": failed_tasks,
                "scheduler_running": self.running
            }
        except Exception as e:
            self.logger.error(f"Error getting scheduler metrics: {e}")
            return {}
