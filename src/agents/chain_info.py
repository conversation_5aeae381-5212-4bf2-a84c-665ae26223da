"""
ChainInfoAgent - Gathers comprehensive blockchain and token metadata.

This agent collects detailed information about tokens including:
- Basic token information (name, symbol, decimals, supply)
- Contract details and deployment information
- Token standards compliance (ERC20, ERC721, etc.)
- Cross-chain token mappings
- Historical deployment and upgrade events
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

import aiohttp
from web3 import Web3
from web3.exceptions import ContractLogicError, BadFunctionCallOutput

from ..core.config import settings
from ..core.database import DatabaseManager
from ..core.cache import CacheManager


class TokenStandard(Enum):
    """Token standard types."""
    ERC20 = "ERC20"
    ERC721 = "ERC721"
    ERC1155 = "ERC1155"
    BEP20 = "BEP20"
    SPL = "SPL"
    UNKNOWN = "UNKNOWN"


@dataclass
class TokenInfo:
    """Comprehensive token information."""
    address: str
    chain_id: int
    name: str
    symbol: str
    decimals: int
    total_supply: int
    max_supply: Optional[int]
    circulating_supply: Optional[int]
    standard: TokenStandard
    
    # Contract details
    creator_address: Optional[str]
    creation_block: Optional[int]
    creation_timestamp: Optional[datetime]
    creation_tx_hash: Optional[str]
    
    # Metadata
    description: Optional[str]
    logo_uri: Optional[str]
    website: Optional[str]
    whitepaper: Optional[str]
    
    # Technical details
    is_mintable: bool
    is_burnable: bool
    is_pausable: bool
    has_fee_on_transfer: bool
    proxy_type: Optional[str]
    implementation_address: Optional[str]
    
    # Cross-chain info
    origin_chain: Optional[int]
    bridge_contracts: List[str]
    
    # Timestamps
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['standard'] = self.standard.value
        result['creation_timestamp'] = self.creation_timestamp.isoformat() if self.creation_timestamp else None
        result['last_updated'] = self.last_updated.isoformat()
        return result


@dataclass
class ChainInfo:
    """Blockchain information."""
    chain_id: int
    name: str
    symbol: str
    rpc_urls: List[str]
    explorer_urls: List[str]
    native_currency: Dict[str, Any]
    block_time: float
    consensus_mechanism: str
    evm_compatible: bool
    layer: str  # L1, L2, sidechain
    bridge_contracts: List[str]
    major_dexes: List[str]
    tvl_usd: Optional[float]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class ChainInfoAgent:
    """Agent for gathering comprehensive chain and token information."""
    
    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        session: Optional[aiohttp.ClientSession] = None
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.session = session
        self.logger = logging.getLogger(__name__)
        
        # Web3 connections
        self.w3_connections = {}
        self._setup_web3_connections()
        
        # Chain information cache
        self.chain_info_cache = {}
        
        # Token list registries
        self.token_registries = {
            1: [  # Ethereum
                "https://tokens.coingecko.com/uniswap/all.json",
                "https://raw.githubusercontent.com/Uniswap/default-token-list/main/src/tokens/mainnet.json"
            ],
            56: [  # BSC
                "https://tokens.pancakeswap.finance/pancakeswap-extended.json"
            ]
        }
        
    def _setup_web3_connections(self):
        """Setup Web3 connections for different chains."""
        rpc_configs = {
            1: {"url": settings.ETHEREUM_RPC_URL, "name": "Ethereum"},
            56: {"url": settings.BSC_RPC_URL, "name": "BSC"},
            137: {"url": settings.POLYGON_RPC_URL, "name": "Polygon"},
            43114: {"url": settings.AVALANCHE_RPC_URL, "name": "Avalanche"},
            250: {"url": settings.FANTOM_RPC_URL, "name": "Fantom"},
            42161: {"url": settings.ARBITRUM_RPC_URL, "name": "Arbitrum"},
            10: {"url": settings.OPTIMISM_RPC_URL, "name": "Optimism"},
        }
        
        for chain_id, config in rpc_configs.items():
            if config["url"]:
                try:
                    self.w3_connections[chain_id] = Web3(Web3.HTTPProvider(config["url"]))
                    self.logger.info(f"Connected to {config['name']} (Chain ID: {chain_id})")
                except Exception as e:
                    self.logger.error(f"Failed to connect to {config['name']}: {e}")
    
    async def get_token_info(
        self,
        token_address: str,
        chain_id: int,
        force_refresh: bool = False
    ) -> TokenInfo:
        """
        Get comprehensive token information.
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            force_refresh: Force refresh cached data
            
        Returns:
            TokenInfo with comprehensive token details
        """
        cache_key = f"token_info:{chain_id}:{token_address.lower()}"
        
        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                self.logger.info(f"Using cached token info for {token_address}")
                token_info = TokenInfo(**cached)
                token_info.standard = TokenStandard(cached['standard'])
                return token_info
        
        self.logger.info(f"Gathering token info for {token_address} on chain {chain_id}")
        
        try:
            w3 = self.w3_connections.get(chain_id)
            if not w3:
                raise ValueError(f"No Web3 connection for chain {chain_id}")
            
            address = Web3.to_checksum_address(token_address)
            
            # Get basic token information
            basic_info = await self._get_basic_token_info(w3, address)
            
            # Get contract deployment info
            deployment_info = await self._get_deployment_info(w3, address)
            
            # Detect token standard
            standard = await self._detect_token_standard(w3, address)
            
            # Get proxy information
            proxy_info = await self._get_proxy_info(w3, address)
            
            # Get external metadata
            metadata = await self._get_external_metadata(token_address, chain_id)
            
            # Check for special features
            features = await self._check_token_features(w3, address)
            
            # Get cross-chain information
            cross_chain_info = await self._get_cross_chain_info(token_address, chain_id)
            
            # Create TokenInfo object
            token_info = TokenInfo(
                address=address,
                chain_id=chain_id,
                name=basic_info.get('name', ''),
                symbol=basic_info.get('symbol', ''),
                decimals=basic_info.get('decimals', 18),
                total_supply=basic_info.get('total_supply', 0),
                max_supply=basic_info.get('max_supply'),
                circulating_supply=basic_info.get('circulating_supply'),
                standard=standard,
                
                creator_address=deployment_info.get('creator_address'),
                creation_block=deployment_info.get('creation_block'),
                creation_timestamp=deployment_info.get('creation_timestamp'),
                creation_tx_hash=deployment_info.get('creation_tx_hash'),
                
                description=metadata.get('description'),
                logo_uri=metadata.get('logo_uri'),
                website=metadata.get('website'),
                whitepaper=metadata.get('whitepaper'),
                
                is_mintable=features.get('is_mintable', False),
                is_burnable=features.get('is_burnable', False),
                is_pausable=features.get('is_pausable', False),
                has_fee_on_transfer=features.get('has_fee_on_transfer', False),
                proxy_type=proxy_info.get('proxy_type'),
                implementation_address=proxy_info.get('implementation_address'),
                
                origin_chain=cross_chain_info.get('origin_chain'),
                bridge_contracts=cross_chain_info.get('bridge_contracts', []),
                
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                token_info.to_dict(),
                ttl=3600  # Cache for 1 hour
            )
            
            # Store in database
            await self._store_token_info(token_info)
            
            self.logger.info(f"Successfully gathered token info for {token_address}")
            return token_info
            
        except Exception as e:
            self.logger.error(f"Failed to get token info for {token_address}: {e}")
            raise
    
    async def _get_basic_token_info(self, w3: Web3, address: str) -> Dict[str, Any]:
        """Get basic ERC20 token information."""
        try:
            # Standard ERC20 ABI
            erc20_abi = [
                {
                    "constant": True,
                    "inputs": [],
                    "name": "name",
                    "outputs": [{"name": "", "type": "string"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "symbol",
                    "outputs": [{"name": "", "type": "string"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "totalSupply",
                    "outputs": [{"name": "", "type": "uint256"}],
                    "type": "function"
                }
            ]
            
            contract = w3.eth.contract(address=address, abi=erc20_abi)
            
            # Get basic info with error handling
            info = {}
            
            try:
                info['name'] = contract.functions.name().call()
            except:
                info['name'] = ''
            
            try:
                info['symbol'] = contract.functions.symbol().call()
            except:
                info['symbol'] = ''
            
            try:
                info['decimals'] = contract.functions.decimals().call()
            except:
                info['decimals'] = 18
            
            try:
                info['total_supply'] = contract.functions.totalSupply().call()
            except:
                info['total_supply'] = 0
            
            # For most tokens, max_supply equals total_supply
            info['max_supply'] = info['total_supply']
            info['circulating_supply'] = info['total_supply']
            
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get basic token info: {e}")
            return {
                'name': '',
                'symbol': '',
                'decimals': 18,
                'total_supply': 0,
                'max_supply': None,
                'circulating_supply': None
            }
    
    async def _get_deployment_info(self, w3: Web3, address: str) -> Dict[str, Any]:
        """Get contract deployment information."""
        try:
            # This would require scanning from genesis or using specialized APIs
            # For now, return placeholder data
            return {
                'creator_address': None,
                'creation_block': None,
                'creation_timestamp': None,
                'creation_tx_hash': None
            }
        except Exception as e:
            self.logger.error(f"Failed to get deployment info: {e}")
            return {}
    
    async def _detect_token_standard(self, w3: Web3, address: str) -> TokenStandard:
        """Detect token standard (ERC20, ERC721, etc.)."""
        try:
            # Check for ERC165 supportsInterface
            erc165_abi = [{
                "constant": True,
                "inputs": [{"name": "interfaceId", "type": "bytes4"}],
                "name": "supportsInterface",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            }]
            
            try:
                contract = w3.eth.contract(address=address, abi=erc165_abi)
                
                # ERC721 interface ID
                if contract.functions.supportsInterface(b'\x80\xac\x58\xcd').call():
                    return TokenStandard.ERC721
                
                # ERC1155 interface ID
                if contract.functions.supportsInterface(b'\xd9\xb6\x7a\x26').call():
                    return TokenStandard.ERC1155
                
            except:
                pass
            
            # Check for standard ERC20 functions
            erc20_functions = ['totalSupply', 'balanceOf', 'transfer', 'transferFrom', 'approve', 'allowance']
            
            for func_name in erc20_functions:
                try:
                    func_abi = [{
                        "constant": True,
                        "inputs": [],
                        "name": func_name,
                        "outputs": [{"name": "", "type": "uint256"}],
                        "type": "function"
                    }]
                    contract = w3.eth.contract(address=address, abi=func_abi)
                    # If we can call totalSupply, it's likely ERC20
                    if func_name == 'totalSupply':
                        contract.functions.totalSupply().call()
                        
                        # Check if we're on BSC for BEP20
                        if w3.eth.chain_id == 56:
                            return TokenStandard.BEP20
                        else:
                            return TokenStandard.ERC20
                except:
                    continue
            
            return TokenStandard.UNKNOWN
            
        except Exception as e:
            self.logger.error(f"Failed to detect token standard: {e}")
            return TokenStandard.UNKNOWN
    
    async def _get_proxy_info(self, w3: Web3, address: str) -> Dict[str, Any]:
        """Get proxy contract information."""
        try:
            # Check common proxy patterns
            proxy_info = {
                'proxy_type': None,
                'implementation_address': None
            }
            
            # EIP-1967 proxy pattern
            implementation_slot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"
            implementation = w3.eth.get_storage_at(address, implementation_slot)
            
            if implementation != b'\x00' * 32:
                proxy_info['proxy_type'] = 'EIP-1967'
                proxy_info['implementation_address'] = Web3.to_checksum_address(implementation[-20:])
            
            # Check for OpenZeppelin proxy pattern
            if not proxy_info['implementation_address']:
                admin_slot = "0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103"
                admin = w3.eth.get_storage_at(address, admin_slot)
                if admin != b'\x00' * 32:
                    proxy_info['proxy_type'] = 'OpenZeppelin'
            
            return proxy_info
            
        except Exception as e:
            self.logger.error(f"Failed to get proxy info: {e}")
            return {'proxy_type': None, 'implementation_address': None}
    
    async def _get_external_metadata(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get token metadata from external sources."""
        try:
            metadata = {}
            
            # Check token lists
            for registry_url in self.token_registries.get(chain_id, []):
                try:
                    if self.session:
                        async with self.session.get(registry_url) as response:
                            if response.status == 200:
                                data = await response.json()
                                token_data = self._find_token_in_registry(data, token_address)
                                if token_data:
                                    metadata.update(token_data)
                                    break
                except Exception as e:
                    self.logger.warning(f"Failed to fetch from {registry_url}: {e}")
            
            # Try CoinGecko API
            if not metadata.get('description'):
                coingecko_data = await self._get_coingecko_metadata(token_address, chain_id)
                metadata.update(coingecko_data)
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Failed to get external metadata: {e}")
            return {}
    
    def _find_token_in_registry(self, registry_data: Dict, token_address: str) -> Dict[str, Any]:
        """Find token in registry data."""
        try:
            tokens = registry_data.get('tokens', [])
            for token in tokens:
                if token.get('address', '').lower() == token_address.lower():
                    return {
                        'description': token.get('description'),
                        'logo_uri': token.get('logoURI'),
                        'website': token.get('website'),
                    }
        except:
            pass
        return {}
    
    async def _get_coingecko_metadata(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get metadata from CoinGecko API."""
        try:
            # Map chain_id to CoinGecko platform
            platform_map = {
                1: 'ethereum',
                56: 'binance-smart-chain',
                137: 'polygon-pos',
                43114: 'avalanche',
                250: 'fantom',
                42161: 'arbitrum-one',
                10: 'optimistic-ethereum'
            }
            
            platform = platform_map.get(chain_id)
            if not platform:
                return {}
            
            # This would use CoinGecko API
            # For now, return empty metadata
            return {}
            
        except Exception as e:
            self.logger.error(f"Failed to get CoinGecko metadata: {e}")
            return {}
    
    async def _check_token_features(self, w3: Web3, address: str) -> Dict[str, bool]:
        """Check for special token features."""
        try:
            features = {
                'is_mintable': False,
                'is_burnable': False,
                'is_pausable': False,
                'has_fee_on_transfer': False
            }
            
            # Check for common function signatures
            function_checks = {
                'is_mintable': ['mint', 'mintTo'],
                'is_burnable': ['burn', 'burnFrom'],
                'is_pausable': ['pause', 'unpause', 'paused'],
            }
            
            for feature, function_names in function_checks.items():
                for func_name in function_names:
                    try:
                        # Try to call the function selector
                        func_signature = Web3.keccak(text=f"{func_name}()")[:4]
                        result = w3.eth.call({
                            'to': address,
                            'data': func_signature
                        })
                        # If call doesn't revert, function likely exists
                        features[feature] = True
                        break
                    except:
                        continue
            
            # Check for transfer fees by simulating a transfer
            # This would require more complex logic
            features['has_fee_on_transfer'] = False
            
            return features
            
        except Exception as e:
            self.logger.error(f"Failed to check token features: {e}")
            return {
                'is_mintable': False,
                'is_burnable': False,
                'is_pausable': False,
                'has_fee_on_transfer': False
            }
    
    async def _get_cross_chain_info(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get cross-chain token information."""
        try:
            # This would check bridge contracts and cross-chain registries
            # For now, return empty data
            return {
                'origin_chain': None,
                'bridge_contracts': []
            }
        except Exception as e:
            self.logger.error(f"Failed to get cross-chain info: {e}")
            return {'origin_chain': None, 'bridge_contracts': []}
    
    async def get_chain_info(self, chain_id: int, force_refresh: bool = False) -> ChainInfo:
        """Get comprehensive blockchain information."""
        cache_key = f"chain_info:{chain_id}"
        
        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                return ChainInfo(**cached)
        
        try:
            # Get chain information
            chain_data = await self._get_chain_metadata(chain_id)
            
            # Get network statistics
            network_stats = await self._get_network_stats(chain_id)
            
            chain_info = ChainInfo(
                chain_id=chain_id,
                name=chain_data.get('name', f'Chain {chain_id}'),
                symbol=chain_data.get('symbol', 'ETH'),
                rpc_urls=chain_data.get('rpc_urls', []),
                explorer_urls=chain_data.get('explorer_urls', []),
                native_currency=chain_data.get('native_currency', {}),
                block_time=network_stats.get('block_time', 12.0),
                consensus_mechanism=chain_data.get('consensus', 'PoS'),
                evm_compatible=chain_data.get('evm_compatible', True),
                layer=chain_data.get('layer', 'L1'),
                bridge_contracts=chain_data.get('bridge_contracts', []),
                major_dexes=chain_data.get('major_dexes', []),
                tvl_usd=network_stats.get('tvl_usd')
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                chain_info.to_dict(),
                ttl=86400  # Cache for 24 hours
            )
            
            return chain_info
            
        except Exception as e:
            self.logger.error(f"Failed to get chain info for {chain_id}: {e}")
            raise
    
    async def _get_chain_metadata(self, chain_id: int) -> Dict[str, Any]:
        """Get static chain metadata."""
        # Static chain information
        chain_configs = {
            1: {
                'name': 'Ethereum',
                'symbol': 'ETH',
                'rpc_urls': [settings.ETHEREUM_RPC_URL] if settings.ETHEREUM_RPC_URL else [],
                'explorer_urls': ['https://etherscan.io'],
                'native_currency': {'name': 'Ether', 'symbol': 'ETH', 'decimals': 18},
                'consensus': 'PoS',
                'evm_compatible': True,
                'layer': 'L1',
                'bridge_contracts': [],
                'major_dexes': ['Uniswap', 'SushiSwap', '1inch']
            },
            56: {
                'name': 'BNB Smart Chain',
                'symbol': 'BNB',
                'rpc_urls': [settings.BSC_RPC_URL] if settings.BSC_RPC_URL else [],
                'explorer_urls': ['https://bscscan.com'],
                'native_currency': {'name': 'BNB', 'symbol': 'BNB', 'decimals': 18},
                'consensus': 'PoA',
                'evm_compatible': True,
                'layer': 'L1',
                'bridge_contracts': [],
                'major_dexes': ['PancakeSwap', 'BiSwap']
            },
            137: {
                'name': 'Polygon',
                'symbol': 'MATIC',
                'rpc_urls': [settings.POLYGON_RPC_URL] if settings.POLYGON_RPC_URL else [],
                'explorer_urls': ['https://polygonscan.com'],
                'native_currency': {'name': 'MATIC', 'symbol': 'MATIC', 'decimals': 18},
                'consensus': 'PoS',
                'evm_compatible': True,
                'layer': 'L2',
                'bridge_contracts': [],
                'major_dexes': ['QuickSwap', 'SushiSwap']
            }
        }
        
        return chain_configs.get(chain_id, {
            'name': f'Chain {chain_id}',
            'symbol': 'ETH',
            'rpc_urls': [],
            'explorer_urls': [],
            'native_currency': {'name': 'Unknown', 'symbol': 'UNK', 'decimals': 18},
            'consensus': 'Unknown',
            'evm_compatible': True,
            'layer': 'Unknown',
            'bridge_contracts': [],
            'major_dexes': []
        })
    
    async def _get_network_stats(self, chain_id: int) -> Dict[str, Any]:
        """Get dynamic network statistics."""
        try:
            w3 = self.w3_connections.get(chain_id)
            if not w3:
                return {}
            
            # Get latest blocks to calculate block time
            latest_block = w3.eth.get_block('latest')
            prev_block = w3.eth.get_block(latest_block.number - 100)
            
            block_time = (latest_block.timestamp - prev_block.timestamp) / 100
            
            # TVL would come from DeFiLlama or similar
            tvl_usd = await self._get_chain_tvl(chain_id)
            
            return {
                'block_time': block_time,
                'tvl_usd': tvl_usd
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get network stats: {e}")
            return {}
    
    async def _get_chain_tvl(self, chain_id: int) -> Optional[float]:
        """Get Total Value Locked for the chain."""
        try:
            # This would integrate with DeFiLlama API
            # For now, return placeholder data
            tvl_data = {
                1: 50_000_000_000,    # Ethereum
                56: 5_000_000_000,    # BSC
                137: 1_000_000_000,   # Polygon
            }
            return tvl_data.get(chain_id)
        except:
            return None
    
    async def _store_token_info(self, token_info: TokenInfo) -> None:
        """Store token information in database."""
        try:
            await self.db_manager.execute("""
                INSERT OR REPLACE INTO token_info (
                    address, chain_id, name, symbol, decimals,
                    total_supply, max_supply, circulating_supply, standard,
                    creator_address, creation_block, creation_timestamp,
                    description, logo_uri, website, whitepaper,
                    is_mintable, is_burnable, is_pausable, has_fee_on_transfer,
                    proxy_type, implementation_address, origin_chain,
                    bridge_contracts, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                token_info.address,
                token_info.chain_id,
                token_info.name,
                token_info.symbol,
                token_info.decimals,
                token_info.total_supply,
                token_info.max_supply,
                token_info.circulating_supply,
                token_info.standard.value,
                token_info.creator_address,
                token_info.creation_block,
                token_info.creation_timestamp.isoformat() if token_info.creation_timestamp else None,
                token_info.description,
                token_info.logo_uri,
                token_info.website,
                token_info.whitepaper,
                token_info.is_mintable,
                token_info.is_burnable,
                token_info.is_pausable,
                token_info.has_fee_on_transfer,
                token_info.proxy_type,
                token_info.implementation_address,
                token_info.origin_chain,
                '\n'.join(token_info.bridge_contracts),
                token_info.last_updated.isoformat()
            ))
            
            self.logger.info(f"Stored token info for {token_info.address}")
            
        except Exception as e:
            self.logger.error(f"Failed to store token info: {e}")
    
    async def search_tokens(
        self,
        query: str,
        chain_id: Optional[int] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Search for tokens by name or symbol."""
        try:
            where_clause = "WHERE (name LIKE ? OR symbol LIKE ?)"
            params = [f"%{query}%", f"%{query}%"]
            
            if chain_id:
                where_clause += " AND chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT address, chain_id, name, symbol, decimals, 
                       total_supply, standard, logo_uri
                FROM token_info
                {where_clause}
                ORDER BY name
                LIMIT ?
            """, params + [limit])
            
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error(f"Failed to search tokens: {e}")
            return []
    
    async def get_token_stats(self, chain_id: Optional[int] = None) -> Dict[str, Any]:
        """Get token statistics."""
        try:
            where_clause = ""
            params = []
            
            if chain_id:
                where_clause = "WHERE chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT 
                    chain_id,
                    standard,
                    COUNT(*) as count,
                    COUNT(CASE WHEN logo_uri IS NOT NULL THEN 1 END) as with_logo,
                    COUNT(CASE WHEN website IS NOT NULL THEN 1 END) as with_website
                FROM token_info
                {where_clause}
                GROUP BY chain_id, standard
                ORDER BY chain_id, count DESC
            """, params)
            
            return {
                'stats': [dict(row) for row in results],
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get token stats: {e}")
            return {'error': str(e)}
