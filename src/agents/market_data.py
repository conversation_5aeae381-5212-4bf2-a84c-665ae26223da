"""
MarketDataAgent - Comprehensive market data collection and analysis.

This agent gathers and analyzes market data from multiple sources including:
- Price data from DEXes and CEXes
- Volume and liquidity metrics
- Trading patterns and market depth
- Historical price analysis
- Market cap and diluted market cap calculations
- Cross-exchange arbitrage opportunities
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
from decimal import Decimal

import aiohttp
import polars as pl
from web3 import Web3

from ..core.config import settings
from ..core.database import DatabaseManager
from ..core.cache import CacheManager


class MarketDataSource(Enum):
    """Market data source types."""
    UNISWAP_V2 = "uniswap_v2"
    UNISWAP_V3 = "uniswap_v3"
    SUSHISWAP = "sushiswap"
    PANCAKESWAP = "pancakeswap"
    COINGECKO = "coingecko"
    COINMARKETCAP = "coinmarketcap"
    DEXSCREENER = "dexscreener"
    DEFILLAMA = "defillama"
    BITQUERY = "bitquery"


class TimeFrame(Enum):
    """Time frame for market data."""
    M1 = "1m"
    M5 = "5m"
    M15 = "15m"
    H1 = "1h"
    H4 = "4h"
    D1 = "1d"
    W1 = "1w"


@dataclass
class PriceData:
    """Price data point."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    volume_usd: float
    source: MarketDataSource
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['source'] = self.source.value
        return result


@dataclass
class MarketMetrics:
    """Comprehensive market metrics."""
    token_address: str
    chain_id: int
    
    # Price metrics
    current_price_usd: float
    price_change_24h: float
    price_change_7d: float
    price_change_30d: float
    
    # Volume metrics
    volume_24h_usd: float
    volume_7d_usd: float
    volume_30d_usd: float
    volume_change_24h: float
    
    # Liquidity metrics
    total_liquidity_usd: float
    liquidity_change_24h: float
    market_cap_usd: Optional[float]
    diluted_market_cap_usd: Optional[float]
    
    # Trading metrics
    number_of_trades_24h: int
    unique_traders_24h: int
    average_trade_size_usd: float
    
    # DEX distribution
    dex_distribution: Dict[str, float]  # DEX -> volume percentage
    largest_dex: str
    largest_dex_volume_pct: float
    
    # Price sources
    price_sources: List[Dict[str, Any]]
    price_consensus: float  # Consensus price across sources
    price_spread: float  # Max spread between sources
    
    # Volatility metrics
    volatility_24h: float
    volatility_7d: float
    volatility_30d: float
    
    # Last update
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['last_updated'] = self.last_updated.isoformat()
        return result


@dataclass
class LiquidityPool:
    """Liquidity pool information."""
    pool_address: str
    dex: str
    token0: str
    token1: str
    token0_symbol: str
    token1_symbol: str
    reserve0: float
    reserve1: float
    total_liquidity_usd: float
    volume_24h_usd: float
    fee_tier: float
    apy: Optional[float]
    created_at: Optional[datetime]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat() if self.created_at else None
        return result


class MarketDataAgent:
    """Agent for comprehensive market data collection and analysis."""
    
    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        session: Optional[aiohttp.ClientSession] = None
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.session = session
        self.logger = logging.getLogger(__name__)
        
        # API endpoints
        self.endpoints = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'dexscreener': 'https://api.dexscreener.com/latest',
            'defillama': 'https://api.llama.fi',
            'bitquery': 'https://graphql.bitquery.io'
        }
        
        # DEX configurations
        self.dex_configs = self._setup_dex_configs()
        
        # Price aggregation weights
        self.source_weights = {
            MarketDataSource.COINGECKO: 0.3,
            MarketDataSource.DEXSCREENER: 0.25,
            MarketDataSource.UNISWAP_V3: 0.2,
            MarketDataSource.UNISWAP_V2: 0.15,
            MarketDataSource.SUSHISWAP: 0.1
        }
    
    def _setup_dex_configs(self) -> Dict[str, Dict[str, Any]]:
        """Setup DEX configurations."""
        return {
            'uniswap_v2': {
                'chain_id': 1,
                'factory_address': '0x5C69bEe701ef814a2B6a3EDD4B1652CB9cc5aA6f',
                'router_address': '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D',
                'fee': 0.003
            },
            'uniswap_v3': {
                'chain_id': 1,
                'factory_address': '0x1F98431c8aD98523631AE4a59f267346ea31F984',
                'quoter_address': '0xb27308f9F90D607463bb33eA1BeBb41C27CE5AB6',
                'fees': [0.0005, 0.003, 0.01]
            },
            'sushiswap': {
                'chain_id': 1,
                'factory_address': '0xC0AEe478e3658e2610c5F7A4A2E1777cE9e4f2Ac',
                'router_address': '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F',
                'fee': 0.003
            },
            'pancakeswap': {
                'chain_id': 56,
                'factory_address': '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73',
                'router_address': '0x10ED43C718714eb63d5aA57B78B54704E256024E',
                'fee': 0.0025
            }
        }
    
    async def get_market_metrics(
        self,
        token_address: str,
        chain_id: int,
        force_refresh: bool = False
    ) -> MarketMetrics:
        """
        Get comprehensive market metrics for a token.
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            force_refresh: Force refresh cached data
            
        Returns:
            MarketMetrics with comprehensive market analysis
        """
        cache_key = f"market_metrics:{chain_id}:{token_address.lower()}"
        
        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                self.logger.info(f"Using cached market metrics for {token_address}")
                return MarketMetrics(**cached)
        
        self.logger.info(f"Gathering market metrics for {token_address} on chain {chain_id}")
        
        try:
            # Gather data from multiple sources in parallel
            tasks = [
                self._get_price_data_multi_source(token_address, chain_id),
                self._get_volume_data(token_address, chain_id),
                self._get_liquidity_data(token_address, chain_id),
                self._get_trading_metrics(token_address, chain_id),
                self._get_dex_distribution(token_address, chain_id),
                self._calculate_volatility(token_address, chain_id)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            price_data = results[0] if not isinstance(results[0], Exception) else {}
            volume_data = results[1] if not isinstance(results[1], Exception) else {}
            liquidity_data = results[2] if not isinstance(results[2], Exception) else {}
            trading_data = results[3] if not isinstance(results[3], Exception) else {}
            dex_data = results[4] if not isinstance(results[4], Exception) else {}
            volatility_data = results[5] if not isinstance(results[5], Exception) else {}
            
            # Calculate market cap
            market_cap_data = await self._calculate_market_cap(
                token_address, chain_id, price_data.get('current_price_usd', 0)
            )
            
            # Create comprehensive metrics
            metrics = MarketMetrics(
                token_address=token_address.lower(),
                chain_id=chain_id,
                
                # Price metrics
                current_price_usd=price_data.get('current_price_usd', 0.0),
                price_change_24h=price_data.get('price_change_24h', 0.0),
                price_change_7d=price_data.get('price_change_7d', 0.0),
                price_change_30d=price_data.get('price_change_30d', 0.0),
                
                # Volume metrics
                volume_24h_usd=volume_data.get('volume_24h_usd', 0.0),
                volume_7d_usd=volume_data.get('volume_7d_usd', 0.0),
                volume_30d_usd=volume_data.get('volume_30d_usd', 0.0),
                volume_change_24h=volume_data.get('volume_change_24h', 0.0),
                
                # Liquidity metrics
                total_liquidity_usd=liquidity_data.get('total_liquidity_usd', 0.0),
                liquidity_change_24h=liquidity_data.get('liquidity_change_24h', 0.0),
                market_cap_usd=market_cap_data.get('market_cap_usd'),
                diluted_market_cap_usd=market_cap_data.get('diluted_market_cap_usd'),
                
                # Trading metrics
                number_of_trades_24h=trading_data.get('trades_24h', 0),
                unique_traders_24h=trading_data.get('unique_traders_24h', 0),
                average_trade_size_usd=trading_data.get('avg_trade_size_usd', 0.0),
                
                # DEX distribution
                dex_distribution=dex_data.get('distribution', {}),
                largest_dex=dex_data.get('largest_dex', ''),
                largest_dex_volume_pct=dex_data.get('largest_dex_pct', 0.0),
                
                # Price sources
                price_sources=price_data.get('sources', []),
                price_consensus=price_data.get('consensus_price', 0.0),
                price_spread=price_data.get('price_spread', 0.0),
                
                # Volatility metrics
                volatility_24h=volatility_data.get('volatility_24h', 0.0),
                volatility_7d=volatility_data.get('volatility_7d', 0.0),
                volatility_30d=volatility_data.get('volatility_30d', 0.0),
                
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                metrics.to_dict(),
                ttl=300  # Cache for 5 minutes
            )
            
            # Store in database
            await self._store_market_metrics(metrics)
            
            self.logger.info(f"Successfully gathered market metrics for {token_address}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to get market metrics for {token_address}: {e}")
            raise
    
    async def _get_price_data_multi_source(
        self, 
        token_address: str, 
        chain_id: int
    ) -> Dict[str, Any]:
        """Get price data from multiple sources and calculate consensus."""
        try:
            sources_data = []
            
            # Get price from different sources
            tasks = [
                self._get_coingecko_price(token_address, chain_id),
                self._get_dexscreener_price(token_address, chain_id),
                self._get_dex_price(token_address, chain_id, 'uniswap_v3'),
                self._get_dex_price(token_address, chain_id, 'uniswap_v2'),
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(results):
                if not isinstance(result, Exception) and result:
                    sources_data.append(result)
            
            if not sources_data:
                return {
                    'current_price_usd': 0.0,
                    'price_change_24h': 0.0,
                    'price_change_7d': 0.0,
                    'price_change_30d': 0.0,
                    'sources': [],
                    'consensus_price': 0.0,
                    'price_spread': 0.0
                }
            
            # Calculate weighted consensus price
            total_weight = 0
            weighted_price = 0
            prices = []
            
            for source_data in sources_data:
                source = source_data.get('source')
                price = source_data.get('price_usd', 0)
                weight = self.source_weights.get(source, 0.1)
                
                if price > 0:
                    weighted_price += price * weight
                    total_weight += weight
                    prices.append(price)
            
            consensus_price = weighted_price / total_weight if total_weight > 0 else 0
            price_spread = (max(prices) - min(prices)) / consensus_price if prices and consensus_price > 0 else 0
            
            # Get price changes (use the most reliable source)
            best_source = max(sources_data, key=lambda x: self.source_weights.get(x.get('source'), 0))
            
            return {
                'current_price_usd': consensus_price,
                'price_change_24h': best_source.get('price_change_24h', 0.0),
                'price_change_7d': best_source.get('price_change_7d', 0.0),
                'price_change_30d': best_source.get('price_change_30d', 0.0),
                'sources': sources_data,
                'consensus_price': consensus_price,
                'price_spread': price_spread
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get multi-source price data: {e}")
            return {}
    
    async def _get_coingecko_price(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get price data from CoinGecko."""
        try:
            # Map chain_id to CoinGecko platform
            platform_map = {
                1: 'ethereum',
                56: 'binance-smart-chain',
                137: 'polygon-pos',
                43114: 'avalanche',
                250: 'fantom',
                42161: 'arbitrum-one',
                10: 'optimistic-ethereum'
            }
            
            platform = platform_map.get(chain_id)
            if not platform:
                return {}
            
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._fetch_coingecko_data(session, token_address, platform)
            else:
                return await self._fetch_coingecko_data(self.session, token_address, platform)
                
        except Exception as e:
            self.logger.error(f"CoinGecko price fetch failed: {e}")
            return {}
    
    async def _fetch_coingecko_data(
        self, 
        session: aiohttp.ClientSession, 
        token_address: str, 
        platform: str
    ) -> Dict[str, Any]:
        """Fetch data from CoinGecko API."""
        try:
            url = f"{self.endpoints['coingecko']}/simple/token_price/{platform}"
            params = {
                'contract_addresses': token_address,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_7d_change': 'true',
                'include_30d_change': 'true'
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    token_data = data.get(token_address.lower(), {})
                    
                    if token_data:
                        return {
                            'source': MarketDataSource.COINGECKO,
                            'price_usd': token_data.get('usd', 0),
                            'price_change_24h': token_data.get('usd_24h_change', 0),
                            'price_change_7d': token_data.get('usd_7d_change', 0),
                            'price_change_30d': token_data.get('usd_30d_change', 0)
                        }
        except Exception as e:
            self.logger.error(f"CoinGecko API error: {e}")
        
        return {}
    
    async def _get_dexscreener_price(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get price data from DexScreener."""
        try:
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._fetch_dexscreener_data(session, token_address, chain_id)
            else:
                return await self._fetch_dexscreener_data(self.session, token_address, chain_id)
                
        except Exception as e:
            self.logger.error(f"DexScreener price fetch failed: {e}")
            return {}
    
    async def _fetch_dexscreener_data(
        self, 
        session: aiohttp.ClientSession, 
        token_address: str, 
        chain_id: int
    ) -> Dict[str, Any]:
        """Fetch data from DexScreener API."""
        try:
            # Map chain_id to DexScreener chain name
            chain_map = {
                1: 'ethereum',
                56: 'bsc',
                137: 'polygon',
                43114: 'avalanche',
                250: 'fantom',
                42161: 'arbitrum',
                10: 'optimism'
            }
            
            chain_name = chain_map.get(chain_id)
            if not chain_name:
                return {}
            
            url = f"{self.endpoints['dexscreener']}/dex/tokens/{token_address}"
            
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    pairs = data.get('pairs', [])
                    
                    if pairs:
                        # Get the most liquid pair
                        best_pair = max(pairs, key=lambda x: float(x.get('liquidity', {}).get('usd', 0)))
                        
                        return {
                            'source': MarketDataSource.DEXSCREENER,
                            'price_usd': float(best_pair.get('priceUsd', 0)),
                            'price_change_24h': float(best_pair.get('priceChange', {}).get('h24', 0)),
                            'price_change_7d': float(best_pair.get('priceChange', {}).get('h7', 0)),
                            'price_change_30d': 0.0,  # Not available in DexScreener
                            'volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                            'liquidity_usd': float(best_pair.get('liquidity', {}).get('usd', 0))
                        }
        except Exception as e:
            self.logger.error(f"DexScreener API error: {e}")
        
        return {}
    
    async def _get_dex_price(self, token_address: str, chain_id: int, dex: str) -> Dict[str, Any]:
        """Get price from specific DEX."""
        try:
            dex_config = self.dex_configs.get(dex)
            if not dex_config or dex_config['chain_id'] != chain_id:
                return {}
            
            # This would implement actual DEX price fetching
            # For now, return placeholder data
            return {
                'source': MarketDataSource(dex),
                'price_usd': 0.0,
                'price_change_24h': 0.0,
                'price_change_7d': 0.0,
                'price_change_30d': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"DEX price fetch failed for {dex}: {e}")
            return {}
    
    async def _get_volume_data(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get comprehensive volume data."""
        try:
            # This would aggregate volume from multiple DEXes
            # For now, return placeholder data
            return {
                'volume_24h_usd': 10000.0,
                'volume_7d_usd': 70000.0,
                'volume_30d_usd': 300000.0,
                'volume_change_24h': 15.5
            }
        except Exception as e:
            self.logger.error(f"Failed to get volume data: {e}")
            return {}
    
    async def _get_liquidity_data(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get liquidity data across DEXes."""
        try:
            # This would aggregate liquidity from multiple DEXes
            # For now, return placeholder data
            return {
                'total_liquidity_usd': 250000.0,
                'liquidity_change_24h': -2.3
            }
        except Exception as e:
            self.logger.error(f"Failed to get liquidity data: {e}")
            return {}
    
    async def _get_trading_metrics(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get trading activity metrics."""
        try:
            # This would analyze on-chain trading data
            # For now, return placeholder data
            return {
                'trades_24h': 450,
                'unique_traders_24h': 180,
                'avg_trade_size_usd': 1250.0
            }
        except Exception as e:
            self.logger.error(f"Failed to get trading metrics: {e}")
            return {}
    
    async def _get_dex_distribution(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get volume distribution across DEXes."""
        try:
            # This would analyze volume across different DEXes
            # For now, return placeholder data
            distribution = {
                'Uniswap V3': 45.2,
                'Uniswap V2': 28.1,
                'SushiSwap': 15.7,
                'Other': 11.0
            }
            
            largest_dex = max(distribution.items(), key=lambda x: x[1])
            
            return {
                'distribution': distribution,
                'largest_dex': largest_dex[0],
                'largest_dex_pct': largest_dex[1]
            }
        except Exception as e:
            self.logger.error(f"Failed to get DEX distribution: {e}")
            return {}
    
    async def _calculate_volatility(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Calculate price volatility metrics."""
        try:
            # Get historical price data
            historical_data = await self._get_historical_prices(token_address, chain_id, days=30)
            
            if len(historical_data) < 2:
                return {
                    'volatility_24h': 0.0,
                    'volatility_7d': 0.0,
                    'volatility_30d': 0.0
                }
            
            # Calculate volatility for different periods
            prices = [point['close'] for point in historical_data]
            
            # Calculate returns
            returns = [
                (prices[i] / prices[i-1] - 1) * 100 
                for i in range(1, len(prices))
            ]
            
            volatility_30d = statistics.stdev(returns) if len(returns) > 1 else 0.0
            volatility_7d = statistics.stdev(returns[-7:]) if len(returns) >= 7 else 0.0
            volatility_24h = statistics.stdev(returns[-1:]) if len(returns) >= 1 else 0.0
            
            return {
                'volatility_24h': volatility_24h,
                'volatility_7d': volatility_7d,
                'volatility_30d': volatility_30d
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate volatility: {e}")
            return {
                'volatility_24h': 0.0,
                'volatility_7d': 0.0,
                'volatility_30d': 0.0
            }
    
    async def _get_historical_prices(
        self, 
        token_address: str, 
        chain_id: int, 
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get historical price data."""
        try:
            # This would fetch historical data from various sources
            # For now, return mock data
            base_price = 1.0
            historical_data = []
            
            for i in range(days):
                timestamp = datetime.utcnow() - timedelta(days=days-i)
                # Mock price with some volatility
                price = base_price * (1 + (i % 7 - 3) * 0.05)
                
                historical_data.append({
                    'timestamp': timestamp,
                    'open': price * 0.98,
                    'high': price * 1.05,
                    'low': price * 0.95,
                    'close': price,
                    'volume': 10000 * (1 + (i % 3) * 0.3)
                })
            
            return historical_data
            
        except Exception as e:
            self.logger.error(f"Failed to get historical prices: {e}")
            return []
    
    async def _calculate_market_cap(
        self, 
        token_address: str, 
        chain_id: int, 
        price_usd: float
    ) -> Dict[str, Any]:
        """Calculate market cap and diluted market cap."""
        try:
            # Get token supply information
            supply_data = await self._get_token_supply(token_address, chain_id)
            
            circulating_supply = supply_data.get('circulating_supply', 0)
            total_supply = supply_data.get('total_supply', 0)
            
            market_cap = circulating_supply * price_usd if circulating_supply else None
            diluted_market_cap = total_supply * price_usd if total_supply else None
            
            return {
                'market_cap_usd': market_cap,
                'diluted_market_cap_usd': diluted_market_cap
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate market cap: {e}")
            return {}
    
    async def _get_token_supply(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Get token supply information."""
        try:
            # This would fetch from token contract or database
            # For now, return placeholder data
            return {
                'circulating_supply': 1000000,
                'total_supply': 1000000
            }
        except Exception as e:
            self.logger.error(f"Failed to get token supply: {e}")
            return {}
    
    async def _store_market_metrics(self, metrics: MarketMetrics) -> None:
        """Store market metrics in database."""
        try:
            await self.db_manager.execute("""
                INSERT OR REPLACE INTO market_metrics (
                    token_address, chain_id, current_price_usd, price_change_24h,
                    price_change_7d, price_change_30d, volume_24h_usd, volume_7d_usd,
                    volume_30d_usd, volume_change_24h, total_liquidity_usd,
                    liquidity_change_24h, market_cap_usd, diluted_market_cap_usd,
                    number_of_trades_24h, unique_traders_24h, average_trade_size_usd,
                    dex_distribution, largest_dex, largest_dex_volume_pct,
                    price_sources, price_consensus, price_spread,
                    volatility_24h, volatility_7d, volatility_30d, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics.token_address,
                metrics.chain_id,
                metrics.current_price_usd,
                metrics.price_change_24h,
                metrics.price_change_7d,
                metrics.price_change_30d,
                metrics.volume_24h_usd,
                metrics.volume_7d_usd,
                metrics.volume_30d_usd,
                metrics.volume_change_24h,
                metrics.total_liquidity_usd,
                metrics.liquidity_change_24h,
                metrics.market_cap_usd,
                metrics.diluted_market_cap_usd,
                metrics.number_of_trades_24h,
                metrics.unique_traders_24h,
                metrics.average_trade_size_usd,
                str(metrics.dex_distribution),
                metrics.largest_dex,
                metrics.largest_dex_volume_pct,
                str(metrics.price_sources),
                metrics.price_consensus,
                metrics.price_spread,
                metrics.volatility_24h,
                metrics.volatility_7d,
                metrics.volatility_30d,
                metrics.last_updated.isoformat()
            ))
            
            self.logger.info(f"Stored market metrics for {metrics.token_address}")
            
        except Exception as e:
            self.logger.error(f"Failed to store market metrics: {e}")
    
    async def get_top_tokens_by_volume(
        self, 
        chain_id: Optional[int] = None, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get top tokens by 24h volume."""
        try:
            where_clause = ""
            params = []
            
            if chain_id:
                where_clause = "WHERE chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT token_address, chain_id, current_price_usd, 
                       volume_24h_usd, price_change_24h, market_cap_usd
                FROM market_metrics
                {where_clause}
                ORDER BY volume_24h_usd DESC
                LIMIT ?
            """, params + [limit])
            
            return [dict(row) for row in results]
            
        except Exception as e:
            self.logger.error(f"Failed to get top tokens by volume: {e}")
            return []
    
    async def get_market_summary(self, chain_id: Optional[int] = None) -> Dict[str, Any]:
        """Get market summary statistics."""
        try:
            where_clause = ""
            params = []
            
            if chain_id:
                where_clause = "WHERE chain_id = ?"
                params.append(chain_id)
            
            results = await self.db_manager.fetch_all(f"""
                SELECT 
                    COUNT(*) as total_tokens,
                    SUM(volume_24h_usd) as total_volume_24h,
                    SUM(total_liquidity_usd) as total_liquidity,
                    AVG(volatility_24h) as avg_volatility,
                    COUNT(CASE WHEN price_change_24h > 0 THEN 1 END) as gainers,
                    COUNT(CASE WHEN price_change_24h < 0 THEN 1 END) as losers
                FROM market_metrics
                {where_clause}
                AND last_updated > datetime('now', '-1 hour')
            """, params)
            
            if results:
                summary = dict(results[0])
                summary['generated_at'] = datetime.utcnow().isoformat()
                return summary
            
            return {'error': 'No data available'}
            
        except Exception as e:
            self.logger.error(f"Failed to get market summary: {e}")
            return {'error': str(e)}
    
    async def get_market_data(
        self,
        token_address: str,
        chain_id: int = 1,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Get market data for a token (wrapper for get_market_metrics).
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            force_refresh: Force refresh cached data
            
        Returns:
            Dict containing market data
        """
        # Use the existing get_market_metrics method and convert to dict
        metrics = await self.get_market_metrics(token_address, chain_id, force_refresh)
        
        # Convert MarketMetrics object to dict for compatibility
        if hasattr(metrics, '__dict__'):
            return metrics.__dict__
        elif hasattr(metrics, 'to_dict'):
            return metrics.to_dict()
        else:
            # Fallback: return basic market data structure
            return {
                "token_address": token_address,
                "chain_id": chain_id,
                "price_usd": getattr(metrics, 'price_usd', 0),
                "volume_24h": getattr(metrics, 'volume_24h', 0),
                "market_cap": getattr(metrics, 'market_cap', 0),
                "sources": getattr(metrics, 'data_sources', []),
                "timestamp": datetime.utcnow().isoformat()
            }
