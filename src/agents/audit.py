"""
Audit Agent for tracking and auditing all system operations.
Provides comprehensive logging, compliance, and security monitoring.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import json
import hashlib

import structlog

from ..core.config import get_settings
from ..core.database import DatabaseMana<PERSON>, AuditRecord
from ..core.cache import CacheManager


logger = structlog.get_logger(__name__)


class AuditLevel:
    """Audit level constants."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    SECURITY = "security"


class AuditAgent:
    """
    Audit agent for comprehensive system monitoring and compliance tracking.
    
    Features:
    - Operation auditing
    - Security event monitoring
    - Compliance tracking
    - Performance monitoring
    - Data integrity verification
    """
    
    def __init__(self, db_manager: DatabaseManager, cache_manager: CacheManager, 
                 metrics_collector, coordinator=None):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        self.coordinator = coordinator
        
        # Audit configuration
        self.audit_enabled = True
        self.audit_levels = [
            AuditLevel.INFO,
            AuditLevel.WARNING,
            AuditLevel.ERROR,
            AuditLevel.CRITICAL,
            AuditLevel.SECURITY
        ]
        
        # Security monitoring
        self.security_events = []
        self.failed_operations = []
        
        # Performance tracking
        self.operation_counts = {}
        self.operation_times = {}
    
    async def initialize(self) -> None:
        """Initialize the audit agent."""
        try:
            self.logger.info("Initializing AuditAgent")
            
            # Log system startup
            await self.log_system_event(
                "system_startup",
                AuditLevel.INFO,
                "AuditAgent initialized successfully"
            )
            
            self.logger.info("AuditAgent initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize AuditAgent: {e}")
            raise
    
    async def shutdown(self) -> None:
        """Shutdown the audit agent."""
        try:
            self.logger.info("Shutting down AuditAgent")
            
            # Log system shutdown
            await self.log_system_event(
                "system_shutdown",
                AuditLevel.INFO,
                "AuditAgent shutting down"
            )
            
            self.logger.info("AuditAgent shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during AuditAgent shutdown: {e}")
    
    async def health_check(self) -> bool:
        """Check agent health."""
        try:
            # Verify database connection
            if self.db_manager:
                await self.db_manager.execute_query("SELECT 1")
            
            # Verify cache connection
            if self.cache_manager:
                await self.cache_manager.set("audit_health_check", "ok", ttl=60)
                result = await self.cache_manager.get("audit_health_check")
                if result != "ok":
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"AuditAgent health check failed: {e}")
            return False
    
    # ==================== AUDIT LOGGING ====================
    
    async def log_operation(
        self,
        operation: str,
        agent: str,
        level: str = AuditLevel.INFO,
        details: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        token_address: Optional[str] = None,
        success: bool = True,
        execution_time_ms: Optional[float] = None
    ) -> str:
        """Log an operation audit record."""
        try:
            if not self.audit_enabled:
                return ""
            
            # Generate audit record
            audit_record = AuditRecord(
                id=self._generate_audit_id(),
                timestamp=datetime.utcnow(),
                operation=operation,
                agent=agent,
                level=level,
                details=details or {},
                user_id=user_id,
                token_address=token_address,
                success=success,
                execution_time_ms=execution_time_ms
            )
            
            # Store in database
            await self._store_audit_record(audit_record)
            
            # Update operation metrics
            self._update_operation_metrics(operation, success, execution_time_ms)
            
            # Check for security events
            if level == AuditLevel.SECURITY or not success:
                await self._process_security_event(audit_record)
            
            return audit_record.id
            
        except Exception as e:
            self.logger.error(f"Failed to log audit record: {e}")
            return ""
    
    async def log_system_event(
        self,
        event: str,
        level: str = AuditLevel.INFO,
        message: str = "",
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log a system-level event."""
        return await self.log_operation(
            operation=f"system_{event}",
            agent="system",
            level=level,
            details={
                "message": message,
                **(details or {})
            }
        )
    
    async def log_security_event(
        self,
        event: str,
        source: str,
        details: Optional[Dict[str, Any]] = None,
        severity: str = "medium"
    ) -> str:
        """Log a security-related event."""
        return await self.log_operation(
            operation=f"security_{event}",
            agent=source,
            level=AuditLevel.SECURITY,
            details={
                "severity": severity,
                "event_type": event,
                **(details or {})
            }
        )
    
    async def log_data_access(
        self,
        data_type: str,
        operation: str,
        user_id: Optional[str] = None,
        token_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log data access for compliance."""
        return await self.log_operation(
            operation=f"data_access_{operation}",
            agent="data_access",
            level=AuditLevel.INFO,
            details={
                "data_type": data_type,
                "access_operation": operation,
                **(details or {})
            },
            user_id=user_id,
            token_address=token_address
        )
    
    # ==================== SECURITY MONITORING ====================
    
    async def _process_security_event(self, audit_record: AuditRecord) -> None:
        """Process security events for alerting and response."""
        try:
            # Add to security events list
            self.security_events.append({
                "timestamp": audit_record.timestamp,
                "operation": audit_record.operation,
                "agent": audit_record.agent,
                "level": audit_record.level,
                "details": audit_record.details
            })
            
            # Keep only recent events (last 1000)
            if len(self.security_events) > 1000:
                self.security_events = self.security_events[-1000:]
            
            # Check for patterns that might indicate security issues
            await self._analyze_security_patterns()
            
            # Alert on critical events
            if audit_record.level == AuditLevel.CRITICAL:
                await self._send_security_alert(audit_record)
            
        except Exception as e:
            self.logger.error(f"Error processing security event: {e}")
    
    async def _analyze_security_patterns(self) -> None:
        """Analyze security events for patterns."""
        try:
            # Check for repeated failures from same agent
            recent_events = [
                event for event in self.security_events[-50:]  # Last 50 events
                if (datetime.utcnow() - event["timestamp"]).total_seconds() < 3600  # Last hour
            ]
            
            # Count failures by agent
            failure_counts = {}
            for event in recent_events:
                if event["level"] in [AuditLevel.ERROR, AuditLevel.CRITICAL]:
                    agent = event["agent"]
                    failure_counts[agent] = failure_counts.get(agent, 0) + 1
            
            # Alert on high failure rates
            for agent, count in failure_counts.items():
                if count > 10:  # More than 10 failures in an hour
                    await self.log_security_event(
                        "high_failure_rate",
                        "audit_agent",
                        {
                            "target_agent": agent,
                            "failure_count": count,
                            "time_window": "1_hour"
                        },
                        severity="high"
                    )
            
        except Exception as e:
            self.logger.error(f"Error analyzing security patterns: {e}")
    
    async def _send_security_alert(self, audit_record: AuditRecord) -> None:
        """Send security alert for critical events."""
        try:
            alert = {
                "type": "security_alert",
                "timestamp": audit_record.timestamp.isoformat(),
                "operation": audit_record.operation,
                "agent": audit_record.agent,
                "level": audit_record.level,
                "details": audit_record.details
            }
            
            # In production, this would send to alerting system
            self.logger.critical("Security alert", **alert)
            
            # Store alert in cache for dashboard
            await self.cache_manager.set(
                f"security_alert_{audit_record.id}",
                alert,
                ttl=86400  # 24 hours
            )
            
        except Exception as e:
            self.logger.error(f"Error sending security alert: {e}")
    
    # ==================== COMPLIANCE TRACKING ====================
    
    async def generate_compliance_report(
        self,
        start_date: datetime,
        end_date: datetime,
        report_type: str = "full"
    ) -> Dict[str, Any]:
        """Generate compliance report for specified period."""
        try:
            # Fetch audit records for period
            records = await self._fetch_audit_records(start_date, end_date)
            
            # Generate report sections
            report = {
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "summary": self._generate_summary_stats(records),
                "operations": self._analyze_operations(records),
                "security_events": self._analyze_security_events(records),
                "compliance_status": self._assess_compliance(records),
                "recommendations": self._generate_recommendations(records)
            }
            
            # Store report
            await self._store_compliance_report(report)
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating compliance report: {e}")
            return {"error": str(e)}
    
    def _generate_summary_stats(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for audit records."""
        total_operations = len(records)
        successful_operations = len([r for r in records if r.get("success", True)])
        failed_operations = total_operations - successful_operations
        
        # Count by level
        level_counts = {}
        for record in records:
            level = record.get("level", AuditLevel.INFO)
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # Count by agent
        agent_counts = {}
        for record in records:
            agent = record.get("agent", "unknown")
            agent_counts[agent] = agent_counts.get(agent, 0) + 1
        
        return {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "failed_operations": failed_operations,
            "success_rate": successful_operations / total_operations if total_operations > 0 else 0,
            "level_distribution": level_counts,
            "agent_distribution": agent_counts
        }
    
    def _analyze_operations(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze operation patterns."""
        operations = {}
        
        for record in records:
            operation = record.get("operation", "unknown")
            if operation not in operations:
                operations[operation] = {
                    "count": 0,
                    "success_count": 0,
                    "failure_count": 0,
                    "total_time_ms": 0,
                    "avg_time_ms": 0
                }
            
            op_data = operations[operation]
            op_data["count"] += 1
            
            if record.get("success", True):
                op_data["success_count"] += 1
            else:
                op_data["failure_count"] += 1
            
            if record.get("execution_time_ms"):
                op_data["total_time_ms"] += record["execution_time_ms"]
                op_data["avg_time_ms"] = op_data["total_time_ms"] / op_data["count"]
        
        return operations
    
    def _analyze_security_events(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze security-related events."""
        security_records = [
            r for r in records 
            if r.get("level") == AuditLevel.SECURITY or not r.get("success", True)
        ]
        
        return {
            "total_security_events": len(security_records),
            "critical_events": len([r for r in security_records if r.get("level") == AuditLevel.CRITICAL]),
            "failed_operations": len([r for r in security_records if not r.get("success", True)]),
            "security_event_rate": len(security_records) / len(records) if records else 0
        }
    
    def _assess_compliance(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess compliance status."""
        # Basic compliance checks
        compliance_score = 1.0
        issues = []
        
        # Check failure rate
        total = len(records)
        failed = len([r for r in records if not r.get("success", True)])
        failure_rate = failed / total if total > 0 else 0
        
        if failure_rate > 0.05:  # >5% failure rate
            compliance_score -= 0.2
            issues.append(f"High failure rate: {failure_rate:.2%}")
        
        # Check for security events
        security_events = len([r for r in records if r.get("level") == AuditLevel.SECURITY])
        if security_events > 0:
            compliance_score -= 0.1 * min(security_events / 10, 0.5)  # Cap at 0.5 reduction
            issues.append(f"Security events detected: {security_events}")
        
        # Determine status
        if compliance_score >= 0.9:
            status = "compliant"
        elif compliance_score >= 0.7:
            status = "minor_issues"
        else:
            status = "non_compliant"
        
        return {
            "status": status,
            "score": compliance_score,
            "issues": issues
        }
    
    def _generate_recommendations(self, records: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on audit analysis."""
        recommendations = []
        
        # Analyze failure patterns
        failed_records = [r for r in records if not r.get("success", True)]
        if len(failed_records) > len(records) * 0.05:  # >5% failure rate
            recommendations.append("Investigate high failure rate and implement error handling improvements")
        
        # Check for security events
        security_events = [r for r in records if r.get("level") == AuditLevel.SECURITY]
        if security_events:
            recommendations.append("Review security events and strengthen security controls")
        
        # Performance recommendations
        slow_operations = [
            r for r in records 
            if r.get("execution_time_ms", 0) > 5000  # >5 seconds
        ]
        if slow_operations:
            recommendations.append("Optimize slow operations to improve system performance")
        
        return recommendations
    
    # ==================== UTILITY METHODS ====================
    
    def _generate_audit_id(self) -> str:
        """Generate unique audit record ID."""
        timestamp = datetime.utcnow().isoformat()
        content = f"{timestamp}_{id(self)}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    async def _store_audit_record(self, record: AuditRecord) -> None:
        """Store audit record in database."""
        try:
            if not self.db_manager:
                return
            
            await self.db_manager.store_audit_record(record)
            
        except Exception as e:
            self.logger.error(f"Failed to store audit record: {e}")
    
    async def _fetch_audit_records(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Fetch audit records from database."""
        try:
            if not self.db_manager:
                return []
            
            records = await self.db_manager.fetch_all("""
                SELECT * FROM audit_records 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            """, (start_date.isoformat(), end_date.isoformat()))
            
            return [dict(record) for record in records]
            
        except Exception as e:
            self.logger.error(f"Failed to fetch audit records: {e}")
            return []
    
    async def _store_compliance_report(self, report: Dict[str, Any]) -> None:
        """Store compliance report."""
        try:
            if not self.db_manager:
                return
            
            await self.db_manager.execute_query("""
                INSERT INTO compliance_reports (
                    id, start_date, end_date, report_data, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                self._generate_audit_id(),
                report["period"]["start"],
                report["period"]["end"],
                json.dumps(report),
                datetime.utcnow().isoformat()
            ))
            
        except Exception as e:
            self.logger.error(f"Failed to store compliance report: {e}")
    
    def _update_operation_metrics(
        self,
        operation: str,
        success: bool,
        execution_time_ms: Optional[float]
    ) -> None:
        """Update operation metrics."""
        if operation not in self.operation_counts:
            self.operation_counts[operation] = {"total": 0, "success": 0, "failure": 0}
        
        self.operation_counts[operation]["total"] += 1
        if success:
            self.operation_counts[operation]["success"] += 1
        else:
            self.operation_counts[operation]["failure"] += 1
        
        if execution_time_ms is not None:
            if operation not in self.operation_times:
                self.operation_times[operation] = []
            self.operation_times[operation].append(execution_time_ms)
            
            # Keep only recent times (last 100)
            if len(self.operation_times[operation]) > 100:
                self.operation_times[operation] = self.operation_times[operation][-100:]
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get audit agent metrics."""
        try:
            return {
                "audit_records_logged": sum(
                    counts["total"] for counts in self.operation_counts.values()
                ),
                "security_events": len(self.security_events),
                "operation_success_rate": self._calculate_overall_success_rate(),
                "average_operation_time": self._calculate_average_operation_time()
            }
        except Exception as e:
            self.logger.error(f"Error getting audit metrics: {e}")
            return {}
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall operation success rate."""
        total_ops = sum(counts["total"] for counts in self.operation_counts.values())
        total_success = sum(counts["success"] for counts in self.operation_counts.values())
        return total_success / total_ops if total_ops > 0 else 0
    
    def _calculate_average_operation_time(self) -> float:
        """Calculate average operation execution time."""
        all_times = []
        for times in self.operation_times.values():
            all_times.extend(times)
        return sum(all_times) / len(all_times) if all_times else 0
