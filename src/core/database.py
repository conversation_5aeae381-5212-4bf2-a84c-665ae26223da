"""
Modern database management using DuckDB for analytics and audit trails.
Provides high-performance analytics with ACID compliance.
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from contextlib import asynccontextmanager

import duckdb
import polars as pl
import structlog
from pydantic import BaseModel

from .config import config


logger = structlog.get_logger(__name__)


class TokenRecord(BaseModel):
    """Token record model for database storage."""
    address: str
    symbol: str
    name: str
    chain: str
    price_usd: float
    market_cap_usd: Optional[float] = None
    volume_24h_usd: Optional[float] = None
    age_days: Optional[int] = None
    verified: bool = False
    discovered_at: datetime
    source: str


class AnalysisRecord(BaseModel):
    """Analysis record model for database storage."""
    id: str
    token_address: str
    chain: str
    risk_score: float
    alpha_score: float
    technical_indicators: Dict[str, Any]
    sentiment_data: Dict[str, Any]
    summary: str
    analyzed_at: datetime
    agent_version: str


class AuditRecord(BaseModel):
    """Audit record model for compliance tracking."""
    id: str
    timestamp: datetime
    operation: str
    agent: str
    level: str
    details: Dict[str, Any] = {}
    user_id: Optional[str] = None
    token_address: Optional[str] = None
    success: bool = True
    execution_time_ms: Optional[float] = None


class DatabaseManager:
    """
    High-performance database manager using DuckDB.
    
    Features:
    - ACID compliance
    - Columnar storage for analytics
    - In-memory processing
    - Automatic schema evolution
    - Backup and recovery
    """
    
    def __init__(self):
        self.db_path = config.database.duckdb_path
        self.connection: Optional[duckdb.DuckDBPyConnection] = None
        self._lock = asyncio.Lock()
        
    async def initialize(self) -> None:
        """Initialize database connection and schema."""
        try:
            # Ensure data directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create connection with minimal settings to avoid unsupported options
            self.connection = duckdb.connect(str(self.db_path))
            
            # Set configuration after connection
            try:
                self.connection.execute(f"SET memory_limit='{config.database.duckdb_memory_limit}'")
                self.connection.execute(f"SET threads={config.database.duckdb_threads}")
            except Exception as config_error:
                logger.warning("Some DuckDB config options not supported", error=str(config_error))
            
            # Enable useful extensions
            await self._execute("INSTALL json;")
            await self._execute("LOAD json;")
            
            # Create schema
            await self._create_schema()
            
            # Create indexes
            await self._create_indexes()
            
            logger.info("Database initialized successfully", path=str(self.db_path))
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            raise
    
    async def close(self) -> None:
        """Close database connection."""
        if self.connection:
            try:
                # Checkpoint before closing
                await self._execute("CHECKPOINT;")
                self.connection.close()
                self.connection = None
                logger.info("Database connection closed")
            except Exception as e:
                logger.error("Error closing database", error=str(e))
    
    async def _execute(self, query: str, params: Optional[Dict] = None) -> Any:
        """Execute query with proper error handling."""
        async with self._lock:
            try:
                if params:
                    return self.connection.execute(query, params).fetchall()
                else:
                    return self.connection.execute(query).fetchall()
            except Exception as e:
                logger.error("Database query failed", query=query, error=str(e))
                raise
    
    async def _create_schema(self) -> None:
        """Create database schema."""
        
        # Tokens table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS tokens (
                address VARCHAR PRIMARY KEY,
                symbol VARCHAR NOT NULL,
                name VARCHAR,
                chain VARCHAR NOT NULL,
                price_usd DOUBLE,
                market_cap_usd DOUBLE,
                volume_24h_usd DOUBLE,
                age_days INTEGER,
                verified BOOLEAN DEFAULT FALSE,
                discovered_at TIMESTAMP NOT NULL,
                source VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Analyses table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS analyses (
                id VARCHAR PRIMARY KEY,
                token_address VARCHAR NOT NULL,
                chain VARCHAR NOT NULL,
                risk_score DOUBLE NOT NULL,
                alpha_score DOUBLE NOT NULL,
                technical_indicators JSON,
                sentiment_data JSON,
                summary TEXT,
                analyzed_at TIMESTAMP NOT NULL,
                agent_version VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (token_address) REFERENCES tokens(address)
            )
        """)
        
        # Audit trail table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS audit_trail (
                id VARCHAR PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                agent_name VARCHAR NOT NULL,
                operation VARCHAR NOT NULL,
                token_address VARCHAR,
                input_hash VARCHAR NOT NULL,
                output_hash VARCHAR NOT NULL,
                status VARCHAR NOT NULL,
                error_message TEXT,
                execution_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Price history table for technical analysis
        await self._execute("""
            CREATE TABLE IF NOT EXISTS price_history (
                token_address VARCHAR NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                price_usd DOUBLE NOT NULL,
                volume_usd DOUBLE,
                market_cap_usd DOUBLE,
                source VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (token_address, timestamp),
                FOREIGN KEY (token_address) REFERENCES tokens(address)
            )
        """)
        
        # Monitoring alerts table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS alerts (
                id VARCHAR PRIMARY KEY,
                token_address VARCHAR NOT NULL,
                alert_type VARCHAR NOT NULL,
                message TEXT NOT NULL,
                severity VARCHAR NOT NULL,
                triggered_at TIMESTAMP NOT NULL,
                acknowledged BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (token_address) REFERENCES tokens(address)
            )
        """)
        
        # Token analyses table (for coordinator)
        await self._execute("""
            CREATE TABLE IF NOT EXISTS token_analyses (
                analysis_id VARCHAR PRIMARY KEY,
                token_address VARCHAR NOT NULL,
                chain_id INTEGER NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                overall_score DOUBLE,
                risk_score DOUBLE,
                investment_recommendation VARCHAR,
                confidence_level DOUBLE,
                execution_time_ms INTEGER,
                data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Agent metrics table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS agent_metrics (
                agent_name VARCHAR PRIMARY KEY,
                execution_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                failure_count INTEGER DEFAULT 0,
                total_execution_time_ms DOUBLE DEFAULT 0,
                average_execution_time_ms DOUBLE DEFAULT 0,
                last_execution TIMESTAMP,
                error_rate DOUBLE DEFAULT 0,
                availability DOUBLE DEFAULT 1,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Metric counters table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS metric_counters (
                name VARCHAR PRIMARY KEY,
                value DOUBLE DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Audit records table (updated format for audit agent)
        await self._execute("""
            CREATE TABLE IF NOT EXISTS audit_records (
                id VARCHAR PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                operation VARCHAR NOT NULL,
                agent VARCHAR NOT NULL,
                level VARCHAR NOT NULL,
                details JSON,
                user_id VARCHAR,
                token_address VARCHAR,
                success BOOLEAN DEFAULT TRUE,
                execution_time_ms DOUBLE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Scheduled tasks table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS scheduled_tasks (
                id VARCHAR PRIMARY KEY,
                name VARCHAR NOT NULL,
                agent VARCHAR NOT NULL,
                method VARCHAR NOT NULL,
                kwargs JSON,
                schedule VARCHAR NOT NULL,
                priority INTEGER NOT NULL,
                status VARCHAR NOT NULL,
                next_run TIMESTAMP,
                last_run TIMESTAMP,
                last_result JSON,
                error_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                timeout_seconds INTEGER DEFAULT 300,
                enabled BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Compliance reports table
        await self._execute("""
            CREATE TABLE IF NOT EXISTS compliance_reports (
                id VARCHAR PRIMARY KEY,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                report_data JSON NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    async def _create_indexes(self) -> None:
        """Create database indexes for performance."""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_tokens_chain ON tokens(chain)",
            "CREATE INDEX IF NOT EXISTS idx_tokens_discovered_at ON tokens(discovered_at)",
            "CREATE INDEX IF NOT EXISTS idx_tokens_price ON tokens(price_usd)",
            "CREATE INDEX IF NOT EXISTS idx_analyses_token ON analyses(token_address)",
            "CREATE INDEX IF NOT EXISTS idx_analyses_analyzed_at ON analyses(analyzed_at)",
            "CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_trail(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_audit_agent ON audit_trail(agent_name)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_timestamp ON price_history(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_alerts_triggered_at ON alerts(triggered_at)",
        ]
        
        for index_query in indexes:
            await self._execute(index_query)
    
    # ==================== TOKEN OPERATIONS ====================
    
    async def insert_token(self, token: TokenRecord) -> None:
        """Insert or update token record."""
        query = """
            INSERT OR REPLACE INTO tokens 
            (address, symbol, name, chain, price_usd, market_cap_usd, volume_24h_usd, 
             age_days, verified, discovered_at, source, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        await self._execute(query, (
            token.address,
            token.symbol,
            token.name,
            token.chain,
            token.price_usd,
            token.market_cap_usd,
            token.volume_24h_usd,
            token.age_days,
            token.verified,
            token.discovered_at,
            token.source
        ))
    
    async def get_token(self, address: str) -> Optional[Dict[str, Any]]:
        """Get token by address."""
        query = "SELECT * FROM tokens WHERE address = ?"
        result = await self._execute(query, (address,))
        
        if result:
            columns = [desc[0] for desc in self.connection.description]
            return dict(zip(columns, result[0]))
        return None
    
    async def get_recent_tokens(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recently discovered tokens."""
        query = """
            SELECT * FROM tokens 
            WHERE discovered_at >= ? 
            ORDER BY discovered_at DESC 
            LIMIT ?
        """
        
        since = datetime.utcnow() - timedelta(hours=hours)
        results = await self._execute(query, (since, limit))
        
        if results:
            columns = [desc[0] for desc in self.connection.description]
            return [dict(zip(columns, row)) for row in results]
        return []
    
    # ==================== ANALYSIS OPERATIONS ====================
    
    async def insert_analysis(self, analysis: AnalysisRecord) -> None:
        """Insert analysis record."""
        query = """
            INSERT INTO analyses 
            (id, token_address, chain, risk_score, alpha_score, technical_indicators, 
             sentiment_data, summary, analyzed_at, agent_version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        await self._execute(query, (
            analysis.id,
            analysis.token_address,
            analysis.chain,
            analysis.risk_score,
            analysis.alpha_score,
            json.dumps(analysis.technical_indicators),
            json.dumps(analysis.sentiment_data),
            analysis.summary,
            analysis.analyzed_at,
            analysis.agent_version
        ))
    
    async def get_analysis(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Get analysis by ID."""
        query = "SELECT * FROM analyses WHERE id = ?"
        result = await self._execute(query, (analysis_id,))
        
        if result:
            columns = [desc[0] for desc in self.connection.description]
            analysis = dict(zip(columns, result[0]))
            
            # Parse JSON fields
            analysis["technical_indicators"] = json.loads(analysis["technical_indicators"])
            analysis["sentiment_data"] = json.loads(analysis["sentiment_data"])
            
            return analysis
        return None
    
    async def get_token_analyses(self, token_address: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get analyses for a specific token."""
        query = """
            SELECT * FROM analyses 
            WHERE token_address = ? 
            ORDER BY analyzed_at DESC 
            LIMIT ?
        """
        
        results = await self._execute(query, (token_address, limit))
        
        if results:
            columns = [desc[0] for desc in self.connection.description]
            analyses = [dict(zip(columns, row)) for row in results]
            
            # Parse JSON fields
            for analysis in analyses:
                analysis["technical_indicators"] = json.loads(analysis["technical_indicators"])
                analysis["sentiment_data"] = json.loads(analysis["sentiment_data"])
            
            return analyses
        return []
    
    # ==================== AUDIT OPERATIONS ====================
    
    async def insert_audit_record(self, record: AuditRecord) -> None:
        """Insert audit record."""
        query = """
            INSERT INTO audit_trail 
            (id, timestamp, agent_name, operation, token_address, input_hash, 
             output_hash, status, error_message, execution_time_ms)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        await self._execute(query, (
            record.id,
            record.timestamp,
            record.agent_name,
            record.operation,
            record.token_address,
            record.input_hash,
            record.output_hash,
            record.status,
            record.error_message,
            record.execution_time_ms
        ))
    
    # ==================== ANALYTICS OPERATIONS ====================
    
    async def get_daily_summary(self) -> Dict[str, Any]:
        """Generate daily summary report."""
        today = datetime.utcnow().date()
        
        # Get token discoveries
        discoveries_query = """
            SELECT COUNT(*) as count, source 
            FROM tokens 
            WHERE DATE(discovered_at) = ? 
            GROUP BY source
        """
        discoveries = await self._execute(discoveries_query, (today,))
        
        # Get analyses count
        analyses_query = """
            SELECT COUNT(*) as count 
            FROM analyses 
            WHERE DATE(analyzed_at) = ?
        """
        analyses_count = await self._execute(analyses_query, (today,))
        
        # Get average scores
        scores_query = """
            SELECT AVG(risk_score) as avg_risk, AVG(alpha_score) as avg_alpha
            FROM analyses 
            WHERE DATE(analyzed_at) = ?
        """
        scores = await self._execute(scores_query, (today,))
        
        return {
            "date": today.isoformat(),
            "discoveries": dict(discoveries) if discoveries else {},
            "analyses_count": analyses_count[0][0] if analyses_count else 0,
            "average_risk_score": float(scores[0][0]) if scores and scores[0][0] else 0,
            "average_alpha_score": float(scores[0][1]) if scores and scores[0][1] else 0
        }
    
    async def get_risk_analysis_report(self) -> Dict[str, Any]:
        """Generate risk analysis report."""
        query = """
            SELECT 
                COUNT(*) as total_tokens,
                AVG(risk_score) as avg_risk,
                COUNT(CASE WHEN risk_score > 70 THEN 1 END) as high_risk,
                COUNT(CASE WHEN risk_score BETWEEN 30 AND 70 THEN 1 END) as medium_risk,
                COUNT(CASE WHEN risk_score < 30 THEN 1 END) as low_risk
            FROM analyses 
            WHERE analyzed_at >= ?
        """
        
        week_ago = datetime.utcnow() - timedelta(days=7)
        result = await self._execute(query, (week_ago,))
        
        if result:
            return {
                "total_tokens": result[0][0],
                "average_risk_score": float(result[0][1]) if result[0][1] else 0,
                "risk_distribution": {
                    "high_risk": result[0][2],
                    "medium_risk": result[0][3],
                    "low_risk": result[0][4]
                }
            }
        return {}
    
    async def get_market_trends_report(self) -> Dict[str, Any]:
        """Generate market trends report."""
        # Implementation for market trends report
        return {}
    
    # ==================== AUDIT OPERATIONS ====================
    
    async def store_audit_record(self, record: AuditRecord) -> None:
        """Store audit record in database."""
        query = """
            INSERT INTO audit_records 
            (id, timestamp, operation, agent, level, details, user_id, 
             token_address, success, execution_time_ms)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        await self._execute(query, (
            record.id,
            record.timestamp,
            record.operation,
            record.agent,
            record.level,
            json.dumps(record.details),
            record.user_id,
            record.token_address,
            record.success,
            record.execution_time_ms
        ))
    
    async def get_audit_records(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        agent: Optional[str] = None,
        operation: Optional[str] = None,
        limit: int = 1000
    ) -> List[Dict[str, Any]]:
        """Get audit records with optional filters."""
        conditions = []
        params = []
        
        if start_date:
            conditions.append("timestamp >= ?")
            params.append(start_date)
        
        if end_date:
            conditions.append("timestamp <= ?")
            params.append(end_date)
        
        if agent:
            conditions.append("agent = ?")
            params.append(agent)
        
        if operation:
            conditions.append("operation = ?")
            params.append(operation)
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        params.append(limit)
        
        query = f"""
            SELECT * FROM audit_records 
            {where_clause}
            ORDER BY timestamp DESC 
            LIMIT ?
        """
        
        results = await self._execute(query, tuple(params))
        
        if results:
            columns = [desc[0] for desc in self.connection.description]
            return [dict(zip(columns, row)) for row in results]
        return []
    
    # ==================== QUERY UTILITIES ====================
    
    async def execute_query(self, query: str, params: Optional[tuple] = None) -> Any:
        """Execute a raw SQL query and return results as dictionaries."""
        async with self._lock:
            try:
                if params:
                    result = self.connection.execute(query, params)
                else:
                    result = self.connection.execute(query)
                
                # Get column names
                columns = [desc[0] for desc in result.description] if result.description else []
                
                # Convert tuples to dictionaries
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]
                
            except Exception as e:
                logger.error("Database query failed", query=query, error=str(e))
                raise
    
    async def fetch_one(self, query: str, params: Optional[tuple] = None) -> Optional[Any]:
        """Fetch single result from query."""
        results = await self.execute_query(query, params)
        return results[0] if results else None
    
    async def fetch_all(self, query: str, params: Optional[tuple] = None) -> List[Any]:
        """Fetch all results from query."""
        return await self.execute_query(query, params)
        # This would typically involve more complex analytics
        # For now, return basic trend data
        query = """
            SELECT 
                chain,
                COUNT(*) as token_count,
                AVG(price_usd) as avg_price,
                AVG(market_cap_usd) as avg_market_cap
            FROM tokens 
            WHERE discovered_at >= ?
            GROUP BY chain
            ORDER BY token_count DESC
        """
        
        week_ago = datetime.utcnow() - timedelta(days=7)
        results = await self._execute(query, (week_ago,))
        
        trends = []
        if results:
            for row in results:
                trends.append({
                    "chain": row[0],
                    "token_count": row[1],
                    "average_price": float(row[2]) if row[2] else 0,
                    "average_market_cap": float(row[3]) if row[3] else 0
                })
        
        return {"trends_by_chain": trends}
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """Generate system performance report."""
        # Agent performance
        agents_query = """
            SELECT 
                agent_name,
                COUNT(*) as operations,
                AVG(execution_time_ms) as avg_execution_time,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as successful,
                COUNT(CASE WHEN status = 'error' THEN 1 END) as errors
            FROM audit_trail 
            WHERE timestamp >= ?
            GROUP BY agent_name
        """
        
        day_ago = datetime.utcnow() - timedelta(days=1)
        results = await self._execute(agents_query, (day_ago,))
        
        agent_performance = []
        if results:
            for row in results:
                agent_performance.append({
                    "agent_name": row[0],
                    "operations": row[1],
                    "avg_execution_time_ms": float(row[2]) if row[2] else 0,
                    "success_rate": row[3] / row[1] if row[1] > 0 else 0,
                    "error_count": row[4]
                })
        
        return {"agent_performance": agent_performance}
    
    # ==================== HEALTH CHECK ====================
    
    async def health_check(self) -> bool:
        """Perform database health check."""
        try:
            await self._execute("SELECT 1")
            return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    # ==================== BACKUP & MAINTENANCE ====================
    
    async def backup_database(self, backup_path: Path) -> None:
        """Create database backup."""
        try:
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            await self._execute(f"EXPORT DATABASE '{backup_path}' (FORMAT PARQUET)")
            logger.info("Database backup created", path=str(backup_path))
        except Exception as e:
            logger.error("Database backup failed", error=str(e))
            raise
    
    async def optimize_database(self) -> None:
        """Optimize database performance."""
        try:
            await self._execute("ANALYZE")
            await self._execute("CHECKPOINT")
            logger.info("Database optimization completed")
        except Exception as e:
            logger.error("Database optimization failed", error=str(e))
            raise
