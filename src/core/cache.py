"""
High-performance caching system using Redis.
Provides intelligent caching strategies for different data types.
"""

import asyncio
import json
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
from functools import wraps

import redis.asyncio as redis
import structlog
from pydantic import BaseModel

from .config import config


logger = structlog.get_logger(__name__)

T = TypeVar('T')


class CacheStats(BaseModel):
    """Cache statistics model."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class CacheConfig(BaseModel):
    """Cache configuration for different data types."""
    ttl: int  # Time to live in seconds
    serializer: str = "json"  # json, pickle, or raw
    compression: bool = False
    namespace: str = "default"


class CacheManager:
    """
    Redis-based cache manager with intelligent strategies.
    
    Features:
    - Multiple serialization formats
    - Namespace support
    - TTL management
    - Circuit breaker pattern
    - Statistics tracking
    - Batch operations
    """
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.stats = CacheStats()
        self._circuit_breaker_failures = 0
        self._circuit_breaker_threshold = 5
        self._circuit_breaker_timeout = 60
        self._circuit_breaker_last_failure = None
        
        # Cache configurations for different data types
        self.cache_configs = {
            "tokens": CacheConfig(
                ttl=config.database.cache_ttl_default,
                namespace="tokens"
            ),
            "market_data": CacheConfig(
                ttl=config.database.cache_ttl_market_data,
                namespace="market"
            ),
            "chain_data": CacheConfig(
                ttl=config.database.cache_ttl_chain_data,
                namespace="chain"
            ),
            "analysis": CacheConfig(
                ttl=1800,  # 30 minutes
                namespace="analysis"
            ),
            "trends": CacheConfig(
                ttl=900,  # 15 minutes
                namespace="trends"
            ),
            "session": CacheConfig(
                ttl=3600,  # 1 hour
                namespace="session",
                serializer="pickle"
            )
        }
    
    async def initialize(self) -> None:
        """Initialize Redis connection."""
        try:
            redis_config = config.get_redis_config()
            
            self.redis_client = redis.from_url(
                redis_config["url"],
                password=redis_config["password"],
                db=redis_config["db"],
                max_connections=redis_config["max_connections"],
                encoding=redis_config["encoding"],
                decode_responses=redis_config["decode_responses"],
                socket_timeout=10,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            
            logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize cache manager", error=str(e))
            raise
    
    async def close(self) -> None:
        """Close Redis connection."""
        if self.redis_client:
            try:
                await self.redis_client.close()
                self.redis_client = None
                logger.info("Cache manager closed")
            except Exception as e:
                logger.error("Error closing cache manager", error=str(e))
    
    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open."""
        if self._circuit_breaker_failures < self._circuit_breaker_threshold:
            return False
        
        if self._circuit_breaker_last_failure is None:
            return False
        
        time_since_failure = (datetime.utcnow() - self._circuit_breaker_last_failure).total_seconds()
        return time_since_failure < self._circuit_breaker_timeout
    
    def _handle_cache_error(self, operation: str, error: Exception) -> None:
        """Handle cache operation errors."""
        self.stats.errors += 1
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = datetime.utcnow()
        
        logger.warning(
            "Cache operation failed",
            operation=operation,
            error=str(error),
            failures=self._circuit_breaker_failures
        )
    
    def _reset_circuit_breaker(self) -> None:
        """Reset circuit breaker on successful operation."""
        if self._circuit_breaker_failures > 0:
            self._circuit_breaker_failures = 0
            self._circuit_breaker_last_failure = None
            logger.info("Circuit breaker reset")
    
    def _get_cache_config(self, data_type: str) -> CacheConfig:
        """Get cache configuration for data type."""
        return self.cache_configs.get(data_type, self.cache_configs["tokens"])
    
    def _make_key(self, key: str, namespace: str = "default") -> str:
        """Create namespaced cache key."""
        return f"{config.system.app_name}:{namespace}:{key}"
    
    def _serialize_value(self, value: Any, serializer: str) -> bytes:
        """Serialize value based on serializer type."""
        if serializer == "json":
            return json.dumps(value, default=str).encode('utf-8')
        elif serializer == "pickle":
            return pickle.dumps(value)
        elif serializer == "raw":
            return value if isinstance(value, bytes) else str(value).encode('utf-8')
        else:
            raise ValueError(f"Unknown serializer: {serializer}")
    
    def _deserialize_value(self, value, serializer: str) -> Any:
        """Deserialize value based on serializer type."""
        if serializer == "json":
            # Handle both string and bytes input
            if isinstance(value, bytes):
                return json.loads(value.decode('utf-8'))
            else:
                return json.loads(value)
        elif serializer == "pickle":
            # Pickle requires bytes
            if isinstance(value, str):
                value = value.encode('utf-8')
            return pickle.loads(value)
        elif serializer == "raw":
            return value
        else:
            raise ValueError(f"Unknown serializer: {serializer}")
    
    # ==================== CORE CACHE OPERATIONS ====================
    
    async def get(
        self, 
        key: str, 
        data_type: str = "default",
        default: Any = None
    ) -> Any:
        """Get value from cache."""
        if self._is_circuit_breaker_open():
            self.stats.misses += 1
            return default
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            value = await self.redis_client.get(cache_key)
            
            if value is None:
                self.stats.misses += 1
                return default
            
            self.stats.hits += 1
            self._reset_circuit_breaker()
            
            return self._deserialize_value(value, cache_config.serializer)
            
        except Exception as e:
            self._handle_cache_error("get", e)
            return default
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        data_type: str = "default",
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in cache."""
        if self._is_circuit_breaker_open():
            return False
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            cache_ttl = ttl or cache_config.ttl
            
            serialized_value = self._serialize_value(value, cache_config.serializer)
            
            result = await self.redis_client.setex(
                cache_key, 
                cache_ttl, 
                serialized_value
            )
            
            self.stats.sets += 1
            self._reset_circuit_breaker()
            
            return result
            
        except Exception as e:
            self._handle_cache_error("set", e)
            return False
    
    async def delete(self, key: str, data_type: str = "default") -> bool:
        """Delete value from cache."""
        if self._is_circuit_breaker_open():
            return False
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            result = await self.redis_client.delete(cache_key)
            
            self.stats.deletes += 1
            self._reset_circuit_breaker()
            
            return bool(result)
            
        except Exception as e:
            self._handle_cache_error("delete", e)
            return False
    
    async def exists(self, key: str, data_type: str = "default") -> bool:
        """Check if key exists in cache."""
        if self._is_circuit_breaker_open():
            return False
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            result = await self.redis_client.exists(cache_key)
            self._reset_circuit_breaker()
            
            return bool(result)
            
        except Exception as e:
            self._handle_cache_error("exists", e)
            return False
    
    # ==================== BATCH OPERATIONS ====================
    
    async def get_many(
        self, 
        keys: List[str], 
        data_type: str = "default"
    ) -> Dict[str, Any]:
        """Get multiple values from cache."""
        if self._is_circuit_breaker_open():
            self.stats.misses += len(keys)
            return {}
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_keys = [self._make_key(key, cache_config.namespace) for key in keys]
            
            values = await self.redis_client.mget(cache_keys)
            
            result = {}
            for i, (original_key, value) in enumerate(zip(keys, values)):
                if value is not None:
                    try:
                        result[original_key] = self._deserialize_value(
                            value, cache_config.serializer
                        )
                        self.stats.hits += 1
                    except Exception as e:
                        logger.warning(f"Failed to deserialize cached value for {original_key}: {e}")
                        self.stats.misses += 1
                else:
                    self.stats.misses += 1
            
            self._reset_circuit_breaker()
            return result
            
        except Exception as e:
            self._handle_cache_error("get_many", e)
            self.stats.misses += len(keys)
            return {}
    
    async def set_many(
        self, 
        items: Dict[str, Any], 
        data_type: str = "default",
        ttl: Optional[int] = None
    ) -> bool:
        """Set multiple values in cache."""
        if self._is_circuit_breaker_open():
            return False
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_ttl = ttl or cache_config.ttl
            
            # Use pipeline for efficiency
            async with self.redis_client.pipeline() as pipe:
                for key, value in items.items():
                    cache_key = self._make_key(key, cache_config.namespace)
                    serialized_value = self._serialize_value(value, cache_config.serializer)
                    pipe.setex(cache_key, cache_ttl, serialized_value)
                
                await pipe.execute()
            
            self.stats.sets += len(items)
            self._reset_circuit_breaker()
            
            return True
            
        except Exception as e:
            self._handle_cache_error("set_many", e)
            return False
    
    # ==================== ADVANCED OPERATIONS ====================
    
    async def increment(self, key: str, amount: int = 1, data_type: str = "default") -> int:
        """Increment a numeric value."""
        if self._is_circuit_breaker_open():
            return 0
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            result = await self.redis_client.incr(cache_key, amount)
            
            # Set TTL if this is a new key
            await self.redis_client.expire(cache_key, cache_config.ttl)
            
            self._reset_circuit_breaker()
            return result
            
        except Exception as e:
            self._handle_cache_error("increment", e)
            return 0
    
    async def add_to_set(
        self, 
        key: str, 
        values: Union[Any, List[Any]], 
        data_type: str = "default"
    ) -> int:
        """Add values to a set."""
        if self._is_circuit_breaker_open():
            return 0
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            if not isinstance(values, list):
                values = [values]
            
            # Serialize values
            serialized_values = [
                self._serialize_value(value, cache_config.serializer) 
                for value in values
            ]
            
            result = await self.redis_client.sadd(cache_key, *serialized_values)
            
            # Set TTL
            await self.redis_client.expire(cache_key, cache_config.ttl)
            
            self._reset_circuit_breaker()
            return result
            
        except Exception as e:
            self._handle_cache_error("add_to_set", e)
            return 0
    
    async def get_set_members(self, key: str, data_type: str = "default") -> List[Any]:
        """Get all members of a set."""
        if self._is_circuit_breaker_open():
            return []
        
        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            
            members = await self.redis_client.smembers(cache_key)
            
            result = [
                self._deserialize_value(member, cache_config.serializer)
                for member in members
            ]
            
            self._reset_circuit_breaker()
            return result
            
        except Exception as e:
            self._handle_cache_error("get_set_members", e)
            return []
    
    # ==================== CACHE PATTERNS ====================
    
    async def get_or_set(
        self,
        key: str,
        fetch_func,
        data_type: str = "default",
        ttl: Optional[int] = None
    ) -> Any:
        """Get value from cache or fetch and cache it."""
        # Try to get from cache first
        value = await self.get(key, data_type)
        
        if value is not None:
            return value
        
        # Fetch the value
        try:
            value = await fetch_func() if asyncio.iscoroutinefunction(fetch_func) else fetch_func()
            
            # Cache the result
            await self.set(key, value, data_type, ttl)
            
            return value
            
        except Exception as e:
            logger.error("Failed to fetch value for cache", key=key, error=str(e))
            raise
    
    def cached(
        self,
        key_template: str = None,
        data_type: str = "default",
        ttl: Optional[int] = None
    ):
        """Decorator for caching function results."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Generate cache key
                if key_template:
                    cache_key = key_template.format(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
                
                return await self.get_or_set(
                    cache_key,
                    lambda: func(*args, **kwargs),
                    data_type,
                    ttl
                )
            
            return wrapper
        return decorator
    
    # ==================== MAINTENANCE OPERATIONS ====================
    
    async def clear_namespace(self, namespace: str) -> int:
        """Clear all keys in a namespace."""
        if self._is_circuit_breaker_open():
            return 0
        
        try:
            pattern = self._make_key("*", namespace)
            keys = await self.redis_client.keys(pattern)
            
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} keys from namespace {namespace}")
                return deleted
            
            return 0
            
        except Exception as e:
            self._handle_cache_error("clear_namespace", e)
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information and statistics."""
        try:
            info = await self.redis_client.info()
            
            return {
                "stats": self.stats.model_dump(),
                "redis_info": {
                    "used_memory": info.get("used_memory_human", "unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                },
                "circuit_breaker": {
                    "failures": self._circuit_breaker_failures,
                    "is_open": self._is_circuit_breaker_open(),
                    "last_failure": self._circuit_breaker_last_failure.isoformat() if self._circuit_breaker_last_failure else None
                }
            }
            
        except Exception as e:
            logger.error("Failed to get cache info", error=str(e))
            return {"error": str(e)}
    
    async def health_check(self) -> bool:
        """Perform cache health check."""
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error("Cache health check failed", error=str(e))
            return False
