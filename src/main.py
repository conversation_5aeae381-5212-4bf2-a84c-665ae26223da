"""
Modern FastMCP server implementation for the Token Analyzer system.
Provides MCP-compliant tools and resources for token discovery and analysis.
"""

import asyncio
import json
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncIterator
from dataclasses import dataclass

import structlog
from mcp.server.fastmcp import FastMC<PERSON>, Context
from mcp.server.fastmcp.prompts import base
from pydantic import BaseModel, Field

from ..core.config import config
from ..core.database import DatabaseManager
from ..core.cache import CacheManager
from ..agents.coordinator import AgentCoordinator
from ..integrations.metrics import MetricsCollector


logger = structlog.get_logger(__name__)


@dataclass
class ServerContext:
    """Server application context."""
    db_manager: DatabaseManager
    cache_manager: CacheManager
    agent_coordinator: AgentCoordinator
    metrics_collector: MetricsCollector
    session_id: str


@asynccontextmanager
async def server_lifespan(server: FastMCP) -> AsyncIterator[ServerContext]:
    """Manage server startup and shutdown lifecycle."""
    session_id = str(uuid.uuid4())
    logger.info("Starting Token Analyzer MCP server", session_id=session_id)
    
    # Initialize core components
    db_manager = DatabaseManager()
    cache_manager = CacheManager()
    metrics_collector = MetricsCollector()
    
    try:
        # Start components
        await db_manager.initialize()
        await cache_manager.initialize()
        await metrics_collector.start()
        
        # Initialize agent coordinator
        agent_coordinator = AgentCoordinator(
            db_manager=db_manager,
            cache_manager=cache_manager,
            metrics_collector=metrics_collector
        )
        await agent_coordinator.initialize()
        
        logger.info("All components initialized successfully")
        
        yield ServerContext(
            db_manager=db_manager,
            cache_manager=cache_manager,
            agent_coordinator=agent_coordinator,
            metrics_collector=metrics_collector,
            session_id=session_id
        )
        
    finally:
        # Cleanup
        logger.info("Shutting down Token Analyzer MCP server")
        await agent_coordinator.shutdown()
        await metrics_collector.stop()
        await cache_manager.close()
        await db_manager.close()


# Create MCP server with lifespan management
mcp = FastMCP(
    name=config.system.mcp_server_name,
    dependencies=[
        "web3>=7.12.0",
        "aiohttp[speedups]>=3.12.13",
        "duckdb>=1.1.3",
        "redis[hiredis]>=5.2.0",
        "structlog>=24.4.0",
        "ta>=0.11.0",
        "polars>=1.12.0"
    ],
    lifespan=server_lifespan
)


# ==================== PYDANTIC MODELS ====================

class TokenDiscoveryRequest(BaseModel):
    """Request model for token discovery."""
    sources: List[str] = Field(
        default=["defillama", "dexscreener", "coinmarketcap"],
        description="Data sources to use for discovery"
    )
    min_age_hours: int = Field(default=24, description="Minimum token age in hours")
    limit: int = Field(default=50, description="Maximum number of tokens to return")


class TokenAnalysisRequest(BaseModel):
    """Request model for token analysis."""
    address: str = Field(description="Token contract address")
    chain: str = Field(default="ethereum", description="Blockchain network")
    include_technical_analysis: bool = Field(default=True, description="Include TA indicators")
    include_sentiment: bool = Field(default=True, description="Include sentiment analysis")


class TokenInfo(BaseModel):
    """Token information model."""
    address: str
    symbol: str
    name: str
    chain: str
    price_usd: float
    market_cap_usd: Optional[float] = None
    volume_24h_usd: Optional[float] = None
    age_days: Optional[int] = None
    verified: bool = False


class AnalysisResult(BaseModel):
    """Token analysis result model."""
    token: TokenInfo
    risk_score: float = Field(ge=0, le=100, description="Risk score (0-100)")
    alpha_score: float = Field(ge=0, le=100, description="Alpha potential score (0-100)")
    technical_indicators: Dict[str, Any] = Field(default_factory=dict)
    sentiment_data: Dict[str, Any] = Field(default_factory=dict)
    summary: str
    analyzed_at: datetime = Field(default_factory=datetime.utcnow)


# ==================== TOOLS ====================

@mcp.tool()
async def discover_tokens(
    request: TokenDiscoveryRequest,
    ctx: Context
) -> List[TokenInfo]:
    """
    Discover new tokens from multiple data sources.
    
    This tool scans various DeFi platforms and exchanges to find newly listed
    or trending tokens that meet specified criteria.
    """
    logger.info("Starting token discovery", request=request.model_dump())
    
    server_ctx = ctx.request_context.lifespan_context
    coordinator = server_ctx.agent_coordinator
    
    try:
        # Use discovery agent
        discovery_result = await coordinator.run_discovery_pipeline(
            sources=request.sources,
            min_age_hours=request.min_age_hours,
            limit=request.limit
        )
        
        # Convert to response format
        tokens = [
            TokenInfo(
                address=token["address"],
                symbol=token["symbol"],
                name=token.get("name", ""),
                chain=token["chain"],
                price_usd=token.get("price_usd", 0.0),
                market_cap_usd=token.get("market_cap_usd"),
                volume_24h_usd=token.get("volume_24h_usd"),
                age_days=token.get("age_days"),
                verified=token.get("verified", False)
            )
            for token in discovery_result["tokens"]
        ]
        
        logger.info("Token discovery completed", count=len(tokens))
        await ctx.info(f"Discovered {len(tokens)} tokens")
        
        return tokens
        
    except Exception as e:
        logger.error("Token discovery failed", error=str(e))
        await ctx.error(f"Discovery failed: {str(e)}")
        raise


@mcp.tool()
async def analyze_token(
    request: TokenAnalysisRequest,
    ctx: Context
) -> AnalysisResult:
    """
    Perform comprehensive analysis of a specific token.
    
    This tool runs the full analysis pipeline including on-chain data,
    market metrics, technical indicators, and sentiment analysis.
    """
    logger.info("Starting token analysis", request=request.model_dump())
    
    server_ctx = ctx.request_context.lifespan_context
    coordinator = server_ctx.agent_coordinator
    
    try:
        # Progress reporting
        await ctx.report_progress(0.1, message="Validating token...")
        
        # Run analysis pipeline
        analysis_result = await coordinator.run_analysis_pipeline(
            address=request.address,
            chain=request.chain,
            include_technical=request.include_technical_analysis,
            include_sentiment=request.include_sentiment,
            progress_callback=lambda p, msg: ctx.report_progress(p, message=msg)
        )
        
        # Convert to response format
        result = AnalysisResult(
            token=TokenInfo(**analysis_result["token"]),
            risk_score=analysis_result["risk_score"],
            alpha_score=analysis_result["alpha_score"],
            technical_indicators=analysis_result.get("technical_indicators", {}),
            sentiment_data=analysis_result.get("sentiment_data", {}),
            summary=analysis_result["summary"]
        )
        
        logger.info("Token analysis completed", 
                   address=request.address,
                   risk_score=result.risk_score,
                   alpha_score=result.alpha_score)
        
        await ctx.info(f"Analysis complete - Risk: {result.risk_score}, Alpha: {result.alpha_score}")
        
        return result
        
    except Exception as e:
        logger.error("Token analysis failed", error=str(e))
        await ctx.error(f"Analysis failed: {str(e)}")
        raise


@mcp.tool()
async def get_market_data(
    address: str,
    chain: str = "ethereum",
    timeframe: str = "24h",
    ctx: Context
) -> Dict[str, Any]:
    """
    Get real-time market data for a token.
    
    Retrieves current price, volume, and market metrics from multiple sources.
    """
    logger.info("Fetching market data", address=address, chain=chain, timeframe=timeframe)
    
    server_ctx = ctx.request_context.lifespan_context
    coordinator = server_ctx.agent_coordinator
    
    try:
        market_data = await coordinator.get_market_data(
            address=address,
            chain=chain,
            timeframe=timeframe
        )
        
        await ctx.info(f"Retrieved market data for {address}")
        return market_data
        
    except Exception as e:
        logger.error("Market data fetch failed", error=str(e))
        await ctx.error(f"Market data fetch failed: {str(e)}")
        raise


@mcp.tool()
async def start_monitoring(
    addresses: List[str],
    chain: str = "ethereum",
    interval_seconds: int = 60,
    ctx: Context
) -> str:
    """
    Start real-time monitoring for specified tokens.
    
    Sets up continuous monitoring with alerts for significant changes.
    """
    logger.info("Starting token monitoring", addresses=addresses, interval=interval_seconds)
    
    server_ctx = ctx.request_context.lifespan_context
    coordinator = server_ctx.agent_coordinator
    
    try:
        monitor_id = await coordinator.start_monitoring(
            addresses=addresses,
            chain=chain,
            interval_seconds=interval_seconds
        )
        
        await ctx.info(f"Started monitoring {len(addresses)} tokens (ID: {monitor_id})")
        return monitor_id
        
    except Exception as e:
        logger.error("Failed to start monitoring", error=str(e))
        await ctx.error(f"Monitoring start failed: {str(e)}")
        raise


# ==================== RESOURCES ====================

@mcp.resource("analytics://reports/{report_type}")
async def get_analytics_report(report_type: str) -> str:
    """
    Get analytical reports in various formats.
    
    Available report types:
    - daily_summary: Daily token discovery and analysis summary
    - risk_analysis: Risk assessment report
    - market_trends: Market trend analysis
    - performance: System performance metrics
    """
    # Get server context from MCP framework
    server_ctx = mcp.get_context().request_context.lifespan_context
    db_manager = server_ctx.db_manager
    
    try:
        if report_type == "daily_summary":
            report = await db_manager.get_daily_summary()
        elif report_type == "risk_analysis":
            report = await db_manager.get_risk_analysis_report()
        elif report_type == "market_trends":
            report = await db_manager.get_market_trends_report()
        elif report_type == "performance":
            report = await db_manager.get_performance_report()
        else:
            raise ValueError(f"Unknown report type: {report_type}")
        
        return json.dumps(report, indent=2, default=str)
        
    except Exception as e:
        logger.error("Failed to generate report", report_type=report_type, error=str(e))
        raise


@mcp.resource("tokens://discovered")
async def get_discovered_tokens() -> str:
    """Get list of recently discovered tokens."""
    server_ctx = mcp.get_context().request_context.lifespan_context
    cache_manager = server_ctx.cache_manager
    
    try:
        tokens = await cache_manager.get("discovered_tokens", default=[])
        return json.dumps(tokens, indent=2, default=str)
    except Exception as e:
        logger.error("Failed to get discovered tokens", error=str(e))
        raise


@mcp.resource("config://system")
async def get_system_config() -> str:
    """Get current system configuration (sanitized)."""
    # Return sanitized config without sensitive data
    sanitized_config = {
        "environment": config.system.environment,
        "app_name": config.system.app_name,
        "app_version": config.system.app_version,
        "scheduler_interval": config.system.scheduler_interval,
        "validation_criteria": {
            "min_liquidity_usd": config.validation.min_liquidity_usd,
            "min_age_days": config.validation.min_age_days,
            "min_market_cap_usd": config.validation.min_market_cap_usd
        }
    }
    return json.dumps(sanitized_config, indent=2)


# ==================== PROMPTS ====================

@mcp.prompt(title="Token Analysis Report")
async def generate_analysis_prompt(
    token_address: str,
    ctx: Context,
    analysis_type: str = "comprehensive"
) -> List[base.Message]:
    """
    Generate a prompt for detailed token analysis.
    
    Creates a structured prompt for LLM-based token analysis
    incorporating all available data sources.
    """
    server_ctx = ctx.request_context.lifespan_context
    coordinator = server_ctx.agent_coordinator
    
    # Get basic token data
    try:
        token_data = await coordinator.get_token_summary(token_address)
        
        system_message = base.SystemMessage(
            f"You are a professional cryptocurrency analyst. Analyze the following token "
            f"with expertise in DeFi, tokenomics, and risk assessment. Provide {analysis_type} analysis."
        )
        
        user_message = base.UserMessage(
            f"Analyze token at address {token_address}:\n\n"
            f"Token Data:\n{json.dumps(token_data, indent=2, default=str)}\n\n"
            f"Please provide:\n"
            f"1. Risk assessment (0-100 scale)\n"
            f"2. Alpha potential (0-100 scale)\n"
            f"3. Key findings and concerns\n"
            f"4. Investment recommendation\n"
            f"5. Key metrics to monitor"
        )
        
        return [system_message, user_message]
        
    except Exception as e:
        logger.error("Failed to generate analysis prompt", error=str(e))
        return [base.UserMessage(f"Error generating prompt: {str(e)}")]


@mcp.prompt(title="Market Trend Analysis")
async def generate_trend_prompt(
    ctx: Context,
    timeframe: str = "24h"
) -> str:
    """Generate a prompt for market trend analysis."""
    server_ctx = ctx.request_context.lifespan_context
    cache_manager = server_ctx.cache_manager
    
    try:
        trends_data = await cache_manager.get(f"market_trends_{timeframe}", default={})
        
        return (
            f"Analyze the current cryptocurrency market trends for the {timeframe} timeframe. "
            f"Market data:\n{json.dumps(trends_data, indent=2, default=str)}\n\n"
            f"Provide insights on:\n"
            f"1. Overall market sentiment\n"
            f"2. Sector performance\n"
            f"3. Risk factors\n"
            f"4. Opportunities"
        )
        
    except Exception as e:
        logger.error("Failed to generate trend prompt", error=str(e))
        return f"Error generating trend prompt: {str(e)}"


# ==================== HEALTH CHECK ====================

@mcp.tool()
async def health_check(ctx: Context) -> Dict[str, Any]:
    """
    Perform comprehensive system health check.
    
    Checks all components and returns status information.
    """
    server_ctx = ctx.request_context.lifespan_context
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "session_id": server_ctx.session_id,
        "components": {}
    }
    
    try:
        # Check database
        db_healthy = await server_ctx.db_manager.health_check()
        health_status["components"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "details": "DuckDB connection active" if db_healthy else "DuckDB connection failed"
        }
        
        # Check cache
        cache_healthy = await server_ctx.cache_manager.health_check()
        health_status["components"]["cache"] = {
            "status": "healthy" if cache_healthy else "unhealthy",
            "details": "Redis connection active" if cache_healthy else "Redis connection failed"
        }
        
        # Check agents
        agents_healthy = await server_ctx.agent_coordinator.health_check()
        health_status["components"]["agents"] = {
            "status": "healthy" if agents_healthy else "unhealthy",
            "details": f"All agents operational" if agents_healthy else "Some agents unavailable"
        }
        
        # Overall health
        all_healthy = all([
            health_status["components"][comp]["status"] == "healthy"
            for comp in health_status["components"]
        ])
        
        if not all_healthy:
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        health_status["status"] = "unhealthy"
        health_status["error"] = str(e)
        return health_status


# ==================== MAIN ENTRY POINT ====================

async def main():
    """Main entry point for the MCP server."""
    try:
        # Configure logging
        structlog.configure(
            processors=[
                structlog.timestamper.TimeStamper(fmt="iso"),
                structlog.processors.add_log_level,
                structlog.processors.JSONRenderer()
            ],
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        logger.info(
            "Starting Token Analyzer MCP Server",
            version=config.system.app_version,
            environment=config.system.environment
        )
        
        # Run the MCP server
        await mcp.run()
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server failed to start", error=str(e))
        raise


if __name__ == "__main__":
    asyncio.run(main())
