"""
Metrics collection and monitoring for the Token Analyzer system.
Provides comprehensive performance monitoring, alerting, and observability.
"""

import asyncio
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

import structlog

from ..core.config import get_settings
from ..core.database import DatabaseManager


logger = structlog.get_logger(__name__)


@dataclass
class MetricPoint:
    """Individual metric data point."""
    timestamp: datetime
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class AgentMetrics:
    """Agent-specific metrics."""
    name: str
    execution_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    total_execution_time_ms: float = 0
    average_execution_time_ms: float = 0
    last_execution: Optional[datetime] = None
    error_rate: float = 0.0
    availability: float = 1.0


class MetricsCollector:
    """
    Collects, stores, and exposes metrics for the token analyzer system.
    
    Features:
    - Agent performance metrics
    - System health metrics
    - Custom business metrics
    - Time-series data storage
    - Alert threshold monitoring
    """
    
    def __init__(self, retention_hours: int = 24):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self.retention_hours = retention_hours
        
        # Metrics storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.agent_metrics: Dict[str, AgentMetrics] = {}
        self.system_metrics: Dict[str, Any] = {}
        
        # Counters and gauges
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        
        # Alert thresholds
        self.alert_thresholds = {
            "agent_error_rate": 0.1,  # 10% error rate
            "agent_avg_execution_time": 5000,  # 5 seconds
            "memory_usage_percent": 85,  # 85% memory usage
            "cache_hit_rate": 0.5  # 50% cache hit rate
        }
        
        # Database manager for persistent storage
        self.db_manager: Optional[DatabaseManager] = None
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.flush_task: Optional[asyncio.Task] = None
        
        self.running = False
    
    async def start(self) -> None:
        """Start the metrics collector."""
        try:
            self.logger.info("Starting MetricsCollector")
            
            # Initialize database connection
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            
            # Start background tasks
            self.running = True
            self.cleanup_task = asyncio.create_task(self._cleanup_old_metrics())
            self.flush_task = asyncio.create_task(self._flush_metrics_to_db())
            
            self.logger.info("MetricsCollector started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start MetricsCollector: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the metrics collector."""
        try:
            self.logger.info("Stopping MetricsCollector")
            
            self.running = False
            
            # Cancel background tasks
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            if self.flush_task:
                self.flush_task.cancel()
                try:
                    await self.flush_task
                except asyncio.CancelledError:
                    pass
            
            # Final flush to database
            await self._flush_metrics_to_db_once()
            
            # Close database connection
            if self.db_manager:
                await self.db_manager.close()
            
            self.logger.info("MetricsCollector stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping MetricsCollector: {e}")
    
    # ==================== METRIC RECORDING ====================
    
    async def record_agent_execution(
        self,
        agent_name: str,
        method_name: str,
        execution_time_ms: float,
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """Record agent execution metrics."""
        try:
            # Update agent metrics
            if agent_name not in self.agent_metrics:
                self.agent_metrics[agent_name] = AgentMetrics(name=agent_name)
            
            metrics = self.agent_metrics[agent_name]
            metrics.execution_count += 1
            metrics.total_execution_time_ms += execution_time_ms
            metrics.average_execution_time_ms = (
                metrics.total_execution_time_ms / metrics.execution_count
            )
            metrics.last_execution = datetime.utcnow()
            
            if success:
                metrics.success_count += 1
            else:
                metrics.failure_count += 1
            
            metrics.error_rate = metrics.failure_count / metrics.execution_count
            
            # Record time-series metrics
            timestamp = datetime.utcnow()
            labels = {
                "agent": agent_name,
                "method": method_name,
                "success": str(success)
            }
            
            self.metrics["agent_execution_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["agent_execution_count"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"agent_{agent_name}_executions"] += 1
            self.counters[f"agent_{agent_name}_{'successes' if success else 'failures'}"] += 1
            
            # Check alerts
            await self._check_agent_alerts(agent_name, metrics)
            
        except Exception as e:
            self.logger.error(f"Error recording agent execution metrics: {e}")
    
    async def record_cache_operation(
        self,
        operation: str,
        hit: bool,
        execution_time_ms: float
    ) -> None:
        """Record cache operation metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "operation": operation,
                "hit": str(hit)
            }
            
            self.metrics["cache_operation_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["cache_operations"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"cache_{operation}_operations"] += 1
            self.counters[f"cache_{'hits' if hit else 'misses'}"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording cache operation metrics: {e}")
    
    async def record_database_operation(
        self,
        operation: str,
        table: str,
        execution_time_ms: float,
        rows_affected: int = 0
    ) -> None:
        """Record database operation metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "operation": operation,
                "table": table
            }
            
            self.metrics["db_operation_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["db_operations"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            if rows_affected > 0:
                self.metrics["db_rows_affected"].append(
                    MetricPoint(timestamp, rows_affected, labels)
                )
            
            # Update counters
            self.counters[f"db_{operation}_operations"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording database operation metrics: {e}")
    
    async def record_api_request(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        execution_time_ms: float
    ) -> None:
        """Record API request metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            }
            
            self.metrics["api_request_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["api_requests"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"api_requests"] += 1
            self.counters[f"api_requests_{status_code}"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording API request metrics: {e}")
    
    async def record_custom_metric(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Record custom metric."""
        try:
            timestamp = datetime.utcnow()
            metric_labels = labels or {}
            
            self.metrics[name].append(
                MetricPoint(timestamp, value, metric_labels)
            )
            
        except Exception as e:
            self.logger.error(f"Error recording custom metric {name}: {e}")
    
    # ==================== METRIC RETRIEVAL ====================
    
    def get_agent_metrics(self) -> Dict[str, AgentMetrics]:
        """Get all agent metrics."""
        return self.agent_metrics.copy()
    
    def get_agent_metric(self, agent_name: str) -> Optional[AgentMetrics]:
        """Get metrics for specific agent."""
        return self.agent_metrics.get(agent_name)
    
    def get_counters(self) -> Dict[str, float]:
        """Get all counter values."""
        return self.counters.copy()
    
    def get_gauges(self) -> Dict[str, float]:
        """Get all gauge values."""
        return self.gauges.copy()
    
    def get_time_series_metrics(
        self,
        metric_name: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[MetricPoint]:
        """Get time-series metrics within time range."""
        metrics = self.metrics.get(metric_name, deque())
        
        if not start_time and not end_time:
            return list(metrics)
        
        filtered_metrics = []
        for metric in metrics:
            if start_time and metric.timestamp < start_time:
                continue
            if end_time and metric.timestamp > end_time:
                continue
            filtered_metrics.append(metric)
        
        return filtered_metrics
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics."""
        try:
            total_executions = sum(m.execution_count for m in self.agent_metrics.values())
            total_failures = sum(m.failure_count for m in self.agent_metrics.values())
            overall_error_rate = total_failures / total_executions if total_executions > 0 else 0
            
            # Calculate average execution time across all agents
            avg_execution_times = [
                m.average_execution_time_ms 
                for m in self.agent_metrics.values() 
                if m.execution_count > 0
            ]
            overall_avg_execution_time = (
                sum(avg_execution_times) / len(avg_execution_times) 
                if avg_execution_times else 0
            )
            
            # Calculate cache hit rate
            cache_hits = self.counters.get("cache_hits", 0)
            cache_misses = self.counters.get("cache_misses", 0)
            cache_operations = cache_hits + cache_misses
            cache_hit_rate = cache_hits / cache_operations if cache_operations > 0 else 0
            
            health_status = "healthy"
            if overall_error_rate > self.alert_thresholds["agent_error_rate"]:
                health_status = "degraded"
            if overall_avg_execution_time > self.alert_thresholds["agent_avg_execution_time"]:
                health_status = "degraded"
            if cache_hit_rate < self.alert_thresholds["cache_hit_rate"]:
                health_status = "degraded"
            
            return {
                "status": health_status,
                "overall_error_rate": overall_error_rate,
                "overall_avg_execution_time_ms": overall_avg_execution_time,
                "cache_hit_rate": cache_hit_rate,
                "total_executions": total_executions,
                "total_failures": total_failures,
                "active_agents": len(self.agent_metrics),
                "uptime_seconds": (datetime.utcnow() - self._start_time).total_seconds() if hasattr(self, '_start_time') else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating system health: {e}")
            return {"status": "error", "error": str(e)}
    
    # ==================== ALERT MONITORING ====================
    
    async def _check_agent_alerts(self, agent_name: str, metrics: AgentMetrics) -> None:
        """Check for agent-specific alert conditions."""
        try:
            alerts = []
            
            # High error rate alert
            if metrics.error_rate > self.alert_thresholds["agent_error_rate"]:
                alerts.append({
                    "type": "high_error_rate",
                    "agent": agent_name,
                    "value": metrics.error_rate,
                    "threshold": self.alert_thresholds["agent_error_rate"],
                    "message": f"Agent {agent_name} has high error rate: {metrics.error_rate:.2%}"
                })
            
            # High execution time alert
            if metrics.average_execution_time_ms > self.alert_thresholds["agent_avg_execution_time"]:
                alerts.append({
                    "type": "high_execution_time",
                    "agent": agent_name,
                    "value": metrics.average_execution_time_ms,
                    "threshold": self.alert_thresholds["agent_avg_execution_time"],
                    "message": f"Agent {agent_name} has high execution time: {metrics.average_execution_time_ms:.0f}ms"
                })
            
            # Log alerts
            for alert in alerts:
                self.logger.warning("Agent alert triggered", **alert)
                
                # Record alert metric
                await self.record_custom_metric(
                    "agent_alerts",
                    1,
                    {"agent": agent_name, "type": alert["type"]}
                )
            
        except Exception as e:
            self.logger.error(f"Error checking agent alerts: {e}")
    
    # ==================== BACKGROUND TASKS ====================
    
    async def _cleanup_old_metrics(self) -> None:
        """Clean up old metrics periodically."""
        while self.running:
            try:
                cutoff_time = datetime.utcnow() - timedelta(hours=self.retention_hours)
                
                for metric_name, metric_deque in self.metrics.items():
                    # Remove old metrics
                    while metric_deque and metric_deque[0].timestamp < cutoff_time:
                        metric_deque.popleft()
                
                self.logger.debug("Cleaned up old metrics")
                
            except Exception as e:
                self.logger.error(f"Error cleaning up metrics: {e}")
            
            await asyncio.sleep(3600)  # Clean up every hour
    
    async def _flush_metrics_to_db(self) -> None:
        """Flush metrics to database periodically."""
        while self.running:
            try:
                await self._flush_metrics_to_db_once()
                await asyncio.sleep(300)  # Flush every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error flushing metrics to database: {e}")
                await asyncio.sleep(60)  # Retry after 1 minute on error
    
    async def _flush_metrics_to_db_once(self) -> None:
        """Perform a single flush of metrics to database."""
        try:
            if not self.db_manager:
                return
            
            # Flush agent metrics
            for agent_name, metrics in self.agent_metrics.items():
                await self.db_manager.execute_query("""
                    INSERT OR REPLACE INTO agent_metrics (
                        agent_name, execution_count, success_count, failure_count,
                        total_execution_time_ms, average_execution_time_ms,
                        last_execution, error_rate, availability, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    agent_name,
                    metrics.execution_count,
                    metrics.success_count,
                    metrics.failure_count,
                    metrics.total_execution_time_ms,
                    metrics.average_execution_time_ms,
                    metrics.last_execution.isoformat() if metrics.last_execution else None,
                    metrics.error_rate,
                    metrics.availability,
                    datetime.utcnow().isoformat()
                ))
            
            # Flush counter metrics
            for counter_name, value in self.counters.items():
                await self.db_manager.execute_query("""
                    INSERT OR REPLACE INTO metric_counters (
                        name, value, updated_at
                    ) VALUES (?, ?, ?)
                """, (
                    counter_name,
                    value,
                    datetime.utcnow().isoformat()
                ))
            
            self.logger.debug("Flushed metrics to database")
            
        except Exception as e:
            self.logger.error(f"Error flushing metrics to database: {e}")
    
    def set_gauge(self, name: str, value: float) -> None:
        """Set gauge value."""
        self.gauges[name] = value
    
    def increment_counter(self, name: str, value: float = 1.0) -> None:
        """Increment counter."""
        self.counters[name] += value
