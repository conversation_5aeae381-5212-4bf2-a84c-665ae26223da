# Agentic Token Discovery & Analysis System

A production-ready, highly reliable agentic system for discovering and analyzing cryptocurrency tokens using the Model Context Protocol (MCP) framework.

## 🚀 Key Features

- **Model Context Protocol Integration**: Built with FastMCP for robust agent communication
- **Multi-Agent Architecture**: 9 specialized agents for comprehensive token analysis
- **Production-Ready Reliability**: Circuit breakers, rate limiting, retry mechanisms
- **Real-time Data Processing**: Async operations with WebSocket support
- **Comprehensive Monitoring**: Structured logging, metrics, and observability
- **Security-First Design**: Encrypted storage, API key management, input validation
- **Scalable Architecture**: Horizontal scaling with Redis coordination

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Server    │    │  Agent Manager  │    │  Data Pipeline  │
│   (FastMCP)     │◄──►│   Coordinator   │◄──►│   Processor     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Agents       │    │   Integrations  │    │    Storage      │
│ • Discovery     │    │ • DeFiLlama     │    │ • DuckDB        │
│ • Validation    │    │ • Web3/Ethereum │    │ • Redis Cache   │
│ • Analysis      │    │ • Coinbase      │    │ • JSON Reports  │
│ • Audit         │    │ • Google Trends │    │ • Audit Logs    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Installation

### Prerequisites
- Python 3.11+
- Redis Server
- Node.js (for MCP tooling)

### Quick Start

```bash
# Clone the repository
git clone <repo-url>
cd agentic-token-analyzer

# Install with uv (recommended)
curl -LsSf https://astral.sh/uv/install.sh | sh
uv sync

# Or with pip
pip install -e .

# Install development dependencies
uv sync --extra dev

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys
```

## ⚙️ Configuration

Create a `.env` file with your API keys:

```env
# Blockchain Data
ETHERSCAN_API_KEY=your_etherscan_key
INFURA_PROJECT_ID=your_infura_key
ALCHEMY_API_KEY=your_alchemy_key

# Exchange APIs
COINBASE_API_KEY=your_coinbase_key
COINBASE_API_SECRET=your_coinbase_secret

# LLM Services
OPENROUTER_API_KEY=your_openrouter_key
ANTHROPIC_API_KEY=your_anthropic_key

# Database
REDIS_URL=redis://localhost:6379
DUCKDB_PATH=./data/analytics.db

# Monitoring
LOG_LEVEL=INFO
ENABLE_METRICS=true
```

## 🚀 Usage

### Start the MCP Server

```bash
# Development mode with hot reload
uv run mcp dev src/main.py

# Production mode
uv run token-analyzer

# With specific config
uv run token-analyzer --config production.yaml
```

### Claude Desktop Integration

```bash
# Install as MCP server for Claude Desktop
uv run mcp install src/main.py --name "Token Analyzer"
```

### API Usage

```python
from src.core.client import TokenAnalyzerClient

async def analyze_token():
    async with TokenAnalyzerClient() as client:
        # Discover new tokens
        tokens = await client.discover_tokens()
        
        # Analyze specific token
        analysis = await client.analyze_token(
            address="0x...", 
            chain="ethereum"
        )
        
        # Get real-time data
        async for update in client.stream_token_data("0x..."):
            print(f"Price update: {update}")
```

## 🤖 Agents

### Core Agents
1. **SchedulerAgent**: Orchestrates pipeline execution
2. **DiscoveryAgent**: Finds new tokens from multiple sources
3. **ChainInfoAgent**: Retrieves on-chain contract data
4. **ValidatorAgent**: Validates tokens against safety criteria
5. **MarketDataAgent**: Collects real-time market data
6. **TrendAgent**: Analyzes social sentiment and trends
7. **TechnicalAgent**: Performs technical analysis
8. **AnalystAgent**: AI-powered comprehensive analysis
9. **AuditAgent**: Logs all operations for compliance

### Agent Features
- **Circuit Breakers**: Automatic failure handling
- **Rate Limiting**: Respects API limits
- **Caching**: Redis-based intelligent caching
- **Retry Logic**: Exponential backoff with jitter
- **Monitoring**: Detailed metrics and logging

## 📊 Monitoring & Observability

- **Structured Logging**: JSON logs with correlation IDs
- **Metrics**: Prometheus-compatible metrics
- **Health Checks**: /health and /metrics endpoints
- **Tracing**: OpenTelemetry integration
- **Alerts**: Configurable alerting rules

## 🔒 Security

- **Input Validation**: Pydantic models for all data
- **API Key Management**: Secure environment-based storage
- **Rate Limiting**: Per-endpoint and global limits
- **Audit Trail**: Complete operation logging
- **Error Handling**: No sensitive data in logs

## 🧪 Testing

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test types
uv run pytest -m "not slow"  # Skip slow tests
uv run pytest -m integration  # Only integration tests
```

## 📈 Performance

- **Async Operations**: Non-blocking I/O throughout
- **Connection Pooling**: Efficient resource usage
- **Caching Strategy**: Multi-layer caching
- **Batch Processing**: Efficient bulk operations
- **Load Balancing**: Ready for horizontal scaling

## 🔄 CI/CD

```bash
# Pre-commit hooks
pre-commit install

# Manual code quality checks
uv run black src tests
uv run ruff check src tests
uv run mypy src
```

## 📚 Documentation

- [API Reference](./docs/api.md)
- [Agent Development](./docs/agents.md)
- [Deployment Guide](./docs/deployment.md)
- [Troubleshooting](./docs/troubleshooting.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Model Context Protocol](https://modelcontextprotocol.io/) team
- [DeFiLlama](https://defillama.com/) for data APIs
- [Web3.py](https://web3py.readthedocs.io/) community
- Open source contributors
