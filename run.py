#!/usr/bin/env python3
"""
Startup script for the Token Analyzer system.
Can run as MCP server or as HTTP API server.
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def run_mcp_server():
    """Run as MCP server."""
    from src.main import mcp
    
    print("Starting Token Analyzer MCP Server...")
    await mcp.run()


def run_api_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = True):
    """Run as HTTP API server."""
    import uvicorn
    
    print(f"Starting Token Analyzer API Server on {host}:{port}")
    uvicorn.run(
        "src.api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Token Analyzer System")
    parser.add_argument(
        "mode",
        choices=["mcp", "api"],
        help="Run mode: 'mcp' for MCP server, 'api' for HTTP API"
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="API server host (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="API server port (default: 8000)"
    )
    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="Disable auto-reload for API server"
    )
    
    args = parser.parse_args()
    
    if args.mode == "mcp":
        asyncio.run(run_mcp_server())
    elif args.mode == "api":
        run_api_server(
            host=args.host,
            port=args.port,
            reload=not args.no_reload
        )


if __name__ == "__main__":
    main()
