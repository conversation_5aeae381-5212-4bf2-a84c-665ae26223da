"""
Simple test to verify our imports and basic setup work
"""

import asyncio
import os
import sys

# Add src to path
sys.path.insert(0, '/Users/<USER>/ar')

# Test imports
print("Testing imports...")

try:
    from src.core.config import config
    print("✅ Config imported successfully")
    print(f"   Environment: {config.system.environment}")
    print(f"   Data dir: {config.system.data_dir}")
except Exception as e:
    print(f"❌ Config import failed: {e}")

try:
    from src.core.database import DatabaseManager
    print("✅ DatabaseManager imported successfully")
except Exception as e:
    print(f"❌ DatabaseManager import failed: {e}")

try:
    from src.core.cache import CacheManager
    print("✅ CacheManager imported successfully")
except Exception as e:
    print(f"❌ CacheManager import failed: {e}")

try:
    from src.integrations.metrics import MetricsCollector
    print("✅ MetricsCollector imported successfully")
except Exception as e:
    print(f"❌ MetricsCollector import failed: {e}")

try:
    from src.agents.discovery import DiscoveryAgent
    print("✅ DiscoveryAgent imported successfully")
except Exception as e:
    print(f"❌ DiscoveryAgent import failed: {e}")

try:
    from src.agents.validator import ValidatorAgent
    print("✅ ValidatorAgent imported successfully")
except Exception as e:
    print(f"❌ ValidatorAgent import failed: {e}")

try:
    from src.agents.market_data import MarketDataAgent
    print("✅ MarketDataAgent imported successfully")
except Exception as e:
    print(f"❌ MarketDataAgent import failed: {e}")

try:
    from src.agents.technical import TechnicalAgent
    print("✅ TechnicalAgent imported successfully")
except Exception as e:
    print(f"❌ TechnicalAgent import failed: {e}")

try:
    from src.agents.sentiment import SentimentAgent
    print("✅ SentimentAgent imported successfully")
except Exception as e:
    print(f"❌ SentimentAgent import failed: {e}")

try:
    from src.agents.coordinator import AgentCoordinator
    print("✅ AgentCoordinator imported successfully")
except Exception as e:
    print(f"❌ AgentCoordinator import failed: {e}")

async def test_basic_setup():
    """Test basic system setup"""
    print("\nTesting basic system setup...")
    
    try:
        db_manager = DatabaseManager()
        print("✅ DatabaseManager instantiated")
        
        await db_manager.initialize()
        print("✅ Database initialized")
        
        await db_manager.close()
        print("✅ Database closed")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        
    try:
        cache_manager = CacheManager()
        print("✅ CacheManager instantiated")
        
        # Skip cache initialization if Redis is not available
        try:
            await cache_manager.initialize()
            print("✅ Cache initialized")
            await cache_manager.close()
            print("✅ Cache closed")
        except Exception as cache_e:
            print(f"⚠️ Cache not available (this is OK): {cache_e}")
            
    except Exception as e:
        print(f"❌ Cache setup failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_basic_setup())
    print("\n🎯 Basic import and setup test complete!")
