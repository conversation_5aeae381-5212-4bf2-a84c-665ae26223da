"""
End-to-End Integration Tests for Crypto Token Discovery & Analysis Backend
Uses real API keys and data for comprehensive testing - NOT MOCKS!
"""

import asyncio
import json
import logging
import os
import pytest
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional

import httpx
from pydantic import BaseModel

# Import our modules
from src.core.config import Settings
from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.agents.coordinator import AgentCoordinator
from src.agents.discovery import TokenDiscoveryAgent
from src.agents.validator import ValidationAgent
from src.agents.market_data import MarketDataAgent
from src.agents.technical import TechnicalAnalysisAgent
from src.agents.sentiment import SentimentAnalysisAgent
from src.integrations.metrics import MetricsCollector

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Real tokens to test with (well-known, stable tokens)
TEST_TOKENS = [
    {
        "symbol": "USDC",
        "address": "0xA0b86a33E6441e7c0F4a7600bbC0C0FeFDAd7e7",  # USDC on Ethereum
        "chain": "ethereum",
        "expected_type": "stablecoin"
    },
    {
        "symbol": "WETH", 
        "address": "******************************************",  # WETH on Ethereum
        "chain": "ethereum",
        "expected_type": "wrapped_native"
    },
    {
        "symbol": "UNI",
        "address": "******************************************",  # Uniswap token
        "chain": "ethereum", 
        "expected_type": "governance"
    }
]

# Free API endpoints for real data
FREE_APIS = {
    "coingecko_free": "https://api.coingecko.com/api/v3",
    "ethereum_rpc": "https://cloudflare-eth.com",  # Free Ethereum RPC
    "dexscreener": "https://api.dexscreener.com/latest/dex",
    "defipulse": "https://data-api.defipulse.com/api/v1"
}


class E2ETestSuite:
    """Comprehensive End-to-End Test Suite with Real APIs"""
    
    def __init__(self):
        self.settings = Settings()
        self.db_manager = None
        self.cache_manager = None
        self.metrics = None
        self.coordinator = None
        self.test_results = {}
        
    async def setup(self):
        """Initialize all components for testing"""
        logger.info("🚀 Starting E2E Test Suite Setup...")
        
        # Initialize core components
        self.db_manager = DatabaseManager(self.settings)
        await self.db_manager.initialize()
        
        self.cache_manager = CacheManager(self.settings)
        await self.cache_manager.initialize()
        
        self.metrics = MetricsCollector()
        
        # Initialize agent coordinator
        self.coordinator = AgentCoordinator(
            db_manager=self.db_manager,
            cache_manager=self.cache_manager,
            metrics=self.metrics,
            settings=self.settings
        )
        await self.coordinator.initialize()
        
        logger.info("✅ E2E Test Suite Setup Complete")
        
    async def cleanup(self):
        """Cleanup resources after testing"""
        logger.info("🧹 Cleaning up E2E Test Suite...")
        
        if self.cache_manager:
            await self.cache_manager.close()
        if self.db_manager:
            await self.db_manager.close()
            
        logger.info("✅ E2E Test Suite Cleanup Complete")

    async def test_real_token_discovery(self) -> Dict:
        """Test token discovery using real APIs"""
        logger.info("🔍 Testing Real Token Discovery...")
        
        results = {}
        
        try:
            # Test discovering trending tokens from DEXScreener (free API)
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FREE_APIS['dexscreener']}/tokens/trending",
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    trending_data = response.json()
                    results["trending_tokens_found"] = len(trending_data.get("pairs", []))
                    results["trending_sample"] = trending_data.get("pairs", [])[:3]
                    logger.info(f"Found {results['trending_tokens_found']} trending tokens")
                else:
                    logger.warning(f"DEXScreener API returned {response.status_code}")
                    
            # Test CoinGecko trending (free tier)
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{FREE_APIS['coingecko_free']}/search/trending",
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    cg_trending = response.json()
                    results["coingecko_trending"] = cg_trending.get("coins", [])[:5]
                    logger.info(f"Found {len(results['coingecko_trending'])} CoinGecko trending tokens")
                    
            # Test our discovery agent with real filters
            discovered_tokens = await self.coordinator.discovery_agent.discover_tokens({
                "min_liquidity": 100000,  # $100k minimum liquidity
                "min_volume_24h": 50000,  # $50k minimum 24h volume
                "max_age_hours": 168,     # 1 week max age
                "chains": ["ethereum", "polygon"],
                "exclude_memecoins": True
            })
            
            results["agent_discovered_count"] = len(discovered_tokens)
            results["agent_discovered_sample"] = discovered_tokens[:3]
            
            # Store results in database
            await self.db_manager.execute_query(
                """
                INSERT INTO discovery_results (
                    timestamp, filters_used, tokens_found, api_sources_used
                ) VALUES (?, ?, ?, ?)
                """,
                (
                    datetime.utcnow(),
                    json.dumps({"min_liquidity": 100000, "min_volume_24h": 50000}),
                    len(discovered_tokens),
                    json.dumps(["dexscreener", "coingecko"])
                )
            )
            
            results["status"] = "success"
            logger.info(f"✅ Token Discovery Test Complete: {len(discovered_tokens)} tokens found")
            
        except Exception as e:
            logger.error(f"❌ Token Discovery Test Failed: {e}")
            results["status"] = "failed"
            results["error"] = str(e)
            
        return results

    async def test_real_token_validation(self) -> Dict:
        """Test token validation with real token data"""
        logger.info("🔍 Testing Real Token Validation...")
        
        results = {}
        
        try:
            validation_results = []
            
            for token_info in TEST_TOKENS:
                logger.info(f"Validating {token_info['symbol']}...")
                
                # Get real contract data from Ethereum
                async with httpx.AsyncClient() as client:
                    # Query Ethereum RPC for contract data
                    rpc_payload = {
                        "jsonrpc": "2.0",
                        "method": "eth_getCode",
                        "params": [token_info["address"], "latest"],
                        "id": 1
                    }
                    
                    response = await client.post(
                        FREE_APIS["ethereum_rpc"],
                        json=rpc_payload,
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        rpc_data = response.json()
                        has_code = rpc_data.get("result", "0x") != "0x"
                        
                        # Validate token using our agent
                        validation_result = await self.coordinator.validation_agent.validate_token({
                            "address": token_info["address"],
                            "symbol": token_info["symbol"],
                            "chain": token_info["chain"],
                            "contract_verified": has_code,
                            "source_code_available": has_code
                        })
                        
                        validation_result["token_symbol"] = token_info["symbol"]
                        validation_result["expected_type"] = token_info["expected_type"]
                        validation_results.append(validation_result)
                        
                        # Store validation result in database
                        await self.db_manager.execute_query(
                            """
                            INSERT INTO validation_results (
                                token_address, symbol, chain, is_valid, 
                                risk_score, validation_timestamp
                            ) VALUES (?, ?, ?, ?, ?, ?)
                            """,
                            (
                                token_info["address"],
                                token_info["symbol"],
                                token_info["chain"],
                                validation_result.get("is_valid", False),
                                validation_result.get("risk_score", 0.5),
                                datetime.utcnow()
                            )
                        )
                        
            results["validated_tokens"] = validation_results
            results["total_validated"] = len(validation_results)
            results["valid_tokens"] = len([r for r in validation_results if r.get("is_valid")])
            results["status"] = "success"
            
            logger.info(f"✅ Token Validation Test Complete: {results['valid_tokens']}/{results['total_validated']} tokens valid")
            
        except Exception as e:
            logger.error(f"❌ Token Validation Test Failed: {e}")
            results["status"] = "failed"
            results["error"] = str(e)
            
        return results

    async def test_real_market_data(self) -> Dict:
        """Test market data collection with real APIs"""
        logger.info("📊 Testing Real Market Data Collection...")
        
        results = {}
        
        try:
            market_data_results = []
            
            for token_info in TEST_TOKENS:
                logger.info(f"Fetching market data for {token_info['symbol']}...")
                
                # Get real market data from CoinGecko (free tier)
                async with httpx.AsyncClient() as client:
                    # First, get token ID from CoinGecko
                    search_response = await client.get(
                        f"{FREE_APIS['coingecko_free']}/search",
                        params={"query": token_info["symbol"]},
                        timeout=30.0
                    )
                    
                    if search_response.status_code == 200:
                        search_data = search_response.json()
                        tokens = search_data.get("coins", [])
                        
                        if tokens:
                            token_id = tokens[0]["id"]
                            
                            # Get detailed market data
                            market_response = await client.get(
                                f"{FREE_APIS['coingecko_free']}/coins/{token_id}",
                                params={"localization": False, "community_data": False},
                                timeout=30.0
                            )
                            
                            if market_response.status_code == 200:
                                market_data = market_response.json()
                                
                                # Process with our market data agent
                                processed_data = await self.coordinator.market_data_agent.collect_market_data({
                                    "symbol": token_info["symbol"],
                                    "address": token_info["address"],
                                    "raw_data": market_data
                                })
                                
                                processed_data["token_symbol"] = token_info["symbol"]
                                processed_data["data_source"] = "coingecko"
                                market_data_results.append(processed_data)
                                
                                # Store in database
                                market_info = market_data.get("market_data", {})
                                await self.db_manager.execute_query(
                                    """
                                    INSERT INTO market_data (
                                        token_address, symbol, price_usd, market_cap,
                                        volume_24h, price_change_24h, data_timestamp
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                    """,
                                    (
                                        token_info["address"],
                                        token_info["symbol"],
                                        market_info.get("current_price", {}).get("usd", 0),
                                        market_info.get("market_cap", {}).get("usd", 0),
                                        market_info.get("total_volume", {}).get("usd", 0),
                                        market_info.get("price_change_percentage_24h", 0),
                                        datetime.utcnow()
                                    )
                                )
                                
                                logger.info(f"Market data for {token_info['symbol']}: ${market_info.get('current_price', {}).get('usd', 'N/A')}")
                    
                    # Rate limiting for free tier
                    await asyncio.sleep(1.2)
                    
            results["market_data_collected"] = market_data_results
            results["total_tokens_processed"] = len(market_data_results)
            results["status"] = "success"
            
            logger.info(f"✅ Market Data Test Complete: {len(market_data_results)} tokens processed")
            
        except Exception as e:
            logger.error(f"❌ Market Data Test Failed: {e}")
            results["status"] = "failed"
            results["error"] = str(e)
            
        return results

    async def test_end_to_end_pipeline(self) -> Dict:
        """Test complete end-to-end pipeline with real data"""
        logger.info("🚀 Testing Complete E2E Pipeline...")
        
        results = {}
        
        try:
            pipeline_start = time.time()
            
            # Run complete analysis pipeline for USDC
            target_token = TEST_TOKENS[0]  # USDC
            
            logger.info(f"Running full pipeline for {target_token['symbol']}...")
            
            # Step 1: Validate token
            validation_result = await self.coordinator.validation_agent.validate_token({
                "address": target_token["address"],
                "symbol": target_token["symbol"],
                "chain": target_token["chain"]
            })
            
            if validation_result.get("is_valid", False):
                # Step 2: Get market data
                market_data = await self.coordinator.market_data_agent.collect_market_data({
                    "symbol": target_token["symbol"],
                    "address": target_token["address"]
                })
                
                # Step 3: Technical analysis
                technical_analysis = await self.coordinator.technical_agent.analyze_token({
                    "symbol": target_token["symbol"],
                    "address": target_token["address"],
                    "price_data": market_data.get("price_data", [])
                })
                
                # Step 4: Sentiment analysis
                sentiment_analysis = await self.coordinator.sentiment_agent.analyze_sentiment({
                    "symbol": target_token["symbol"],
                    "address": target_token["address"]
                })
                
                # Step 5: Generate comprehensive report
                final_report = {
                    "token": target_token,
                    "validation": validation_result,
                    "market_data": market_data,
                    "technical_analysis": technical_analysis,
                    "sentiment_analysis": sentiment_analysis,
                    "analysis_timestamp": datetime.utcnow().isoformat(),
                    "pipeline_duration": time.time() - pipeline_start
                }
                
                # Store complete analysis
                await self.db_manager.execute_query(
                    """
                    INSERT INTO comprehensive_analyses (
                        token_address, symbol, analysis_data, 
                        overall_score, analysis_timestamp
                    ) VALUES (?, ?, ?, ?, ?)
                    """,
                    (
                        target_token["address"],
                        target_token["symbol"],
                        json.dumps(final_report),
                        (validation_result.get("confidence", 0) + 
                         technical_analysis.get("confidence_score", 0) + 
                         sentiment_analysis.get("confidence", 0)) / 3,
                        datetime.utcnow()
                    )
                )
                
                results["pipeline_successful"] = True
                results["pipeline_duration"] = time.time() - pipeline_start
                results["analysis_report"] = final_report
                results["overall_confidence"] = final_report.get("overall_score", 0)
                
            else:
                results["pipeline_successful"] = False
                results["failure_reason"] = "Token validation failed"
                
            results["status"] = "success"
            logger.info(f"✅ Full Pipeline Test Complete in {results.get('pipeline_duration', 0):.2f}s")
            
        except Exception as e:
            logger.error(f"❌ Full Pipeline Test Failed: {e}")
            results["status"] = "failed"
            results["error"] = str(e)
            
        return results

    async def run_comprehensive_tests(self) -> Dict:
        """Run all tests and return comprehensive results"""
        logger.info("🎯 Starting Comprehensive E2E Tests...")
        
        suite_start = time.time()
        all_results = {}
        
        try:
            await self.setup()
            
            # Run test categories
            test_categories = [
                ("token_discovery", self.test_real_token_discovery),
                ("token_validation", self.test_real_token_validation),
                ("market_data", self.test_real_market_data),
                ("end_to_end_pipeline", self.test_end_to_end_pipeline)
            ]
            
            for category_name, test_func in test_categories:
                logger.info(f"🔄 Running {category_name} tests...")
                category_start = time.time()
                
                try:
                    category_results = await test_func()
                    category_results["duration"] = time.time() - category_start
                    all_results[category_name] = category_results
                    
                    if category_results.get("status") == "success":
                        logger.info(f"✅ {category_name} tests passed")
                    else:
                        logger.warning(f"⚠️ {category_name} tests had issues")
                        
                except Exception as e:
                    logger.error(f"❌ {category_name} tests failed: {e}")
                    all_results[category_name] = {
                        "status": "failed",
                        "error": str(e),
                        "duration": time.time() - category_start
                    }
                    
                # Brief pause between test categories
                await asyncio.sleep(0.5)
                
            # Generate final summary
            total_duration = time.time() - suite_start
            passed_tests = len([r for r in all_results.values() if r.get("status") == "success"])
            total_tests = len(all_results)
            
            summary = {
                "total_duration": total_duration,
                "total_test_categories": total_tests,
                "passed_categories": passed_tests,
                "failed_categories": total_tests - passed_tests,
                "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            all_results["test_summary"] = summary
            
            logger.info(f"🏁 E2E Test Suite Complete!")
            logger.info(f"📊 Results: {passed_tests}/{total_tests} categories passed ({summary['success_rate']:.1f}%)")
            logger.info(f"⏱️ Total Duration: {total_duration:.2f} seconds")
            
        except Exception as e:
            logger.error(f"❌ Test Suite Setup Failed: {e}")
            all_results["setup_error"] = str(e)
            
        finally:
            await self.cleanup()
            
        return all_results


# Test fixtures and actual test functions
@pytest.fixture(scope="session")
async def e2e_test_suite():
    """Initialize E2E test suite for pytest"""
    suite = E2ETestSuite()
    await suite.setup()
    
    yield suite
    
    await suite.cleanup()


@pytest.mark.asyncio
async def test_real_token_discovery():
    """Test real token discovery with actual APIs"""
    suite = E2ETestSuite()
    await suite.setup()
    
    try:
        results = await suite.test_real_token_discovery()
        assert results["status"] == "success"
        assert "trending_tokens_found" in results
        print(f"\n✅ Discovered tokens: {results.get('agent_discovered_count', 0)}")
        print(f"📊 Trending tokens found: {results.get('trending_tokens_found', 0)}")
    finally:
        await suite.cleanup()


@pytest.mark.asyncio  
async def test_real_token_validation():
    """Test real token validation with actual contract data"""
    suite = E2ETestSuite()
    await suite.setup()
    
    try:
        results = await suite.test_real_token_validation()
        assert results["status"] == "success"
        assert results["total_validated"] > 0
        print(f"\n✅ Validated tokens: {results['valid_tokens']}/{results['total_validated']}")
        
        # Print validation details
        for token_result in results.get("validated_tokens", []):
            symbol = token_result.get("token_symbol", "Unknown")
            is_valid = token_result.get("is_valid", False)
            risk_score = token_result.get("risk_score", 0)
            print(f"   {symbol}: Valid={is_valid}, Risk={risk_score:.2f}")
            
    finally:
        await suite.cleanup()


@pytest.mark.asyncio
async def test_real_market_data():
    """Test real market data collection"""
    suite = E2ETestSuite()
    await suite.setup()
    
    try:
        results = await suite.test_real_market_data()
        assert results["status"] == "success"
        assert results["total_tokens_processed"] > 0
        print(f"\n✅ Market data collected for {results['total_tokens_processed']} tokens")
        
        # Print market data details
        for market_result in results.get("market_data_collected", []):
            symbol = market_result.get("token_symbol", "Unknown")
            price = market_result.get("price_usd", 0)
            print(f"   {symbol}: ${price}")
            
    finally:
        await suite.cleanup()


@pytest.mark.asyncio
async def test_complete_e2e_pipeline():
    """Test the complete end-to-end pipeline with real data"""
    suite = E2ETestSuite()
    
    print("\n🚀 Starting Complete E2E Pipeline Test with REAL DATA")
    print("=" * 60)
    
    results = await suite.run_comprehensive_tests()
    
    # Print detailed results
    print(f"\n📋 DETAILED TEST RESULTS")
    print("=" * 60)
    
    for category, result in results.items():
        if category == "test_summary":
            continue
            
        print(f"\n🔍 {category.upper().replace('_', ' ')}")
        print("-" * 40)
        
        status = result.get("status", "unknown")
        duration = result.get("duration", 0)
        
        if status == "success":
            print(f"✅ Status: PASSED ({duration:.2f}s)")
        else:
            print(f"❌ Status: FAILED ({duration:.2f}s)")
            if "error" in result:
                print(f"   Error: {result['error']}")
                
        # Print key metrics for each category
        for key, value in result.items():
            if key not in ["status", "error", "duration"]:
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value}")
                elif isinstance(value, str) and len(value) < 100:
                    print(f"   {key}: {value}")
                elif isinstance(value, list) and len(value) <= 3:
                    print(f"   {key}: {len(value)} items")
                    
    # Print final summary
    if "test_summary" in results:
        summary = results["test_summary"]
        print(f"\n🏆 FINAL SUMMARY")
        print("=" * 60)
        print(f"📊 Success Rate: {summary['success_rate']:.1f}%")
        print(f"✅ Passed: {summary['passed_categories']}/{summary['total_test_categories']}")
        print(f"⏱️ Total Time: {summary['total_duration']:.2f} seconds")
        print(f"🕒 Completed: {summary['timestamp']}")
        
        # Verify overall success
        assert summary["success_rate"] >= 50.0, f"Too many tests failed: {summary['success_rate']:.1f}% success rate"
        
    return results


# Main test execution function
async def main():
    """Run the E2E test suite independently"""
    logger.info("🚀 Starting Crypto Token Analysis E2E Test Suite")
    logger.info("=" * 60)
    
    test_suite = E2ETestSuite()
    results = await test_suite.run_comprehensive_tests()
    
    return results


if __name__ == "__main__":
    # Run tests independently
    results = asyncio.run(main())
