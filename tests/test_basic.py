"""
Basic test to verify the system can initialize properly.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock

from src.core.config import get_settings
from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.integrations.metrics import MetricsCollector
from src.agents.coordinator import AgentCoordinator


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    settings = get_settings()
    # Override with test values
    settings.database.duckdb_path = ":memory:"
    settings.cache.redis_url = "redis://localhost:6379/15"  # Test database
    return settings


@pytest.mark.asyncio
async def test_database_initialization(mock_settings):
    """Test database manager initialization."""
    db_manager = DatabaseManager()
    
    try:
        await db_manager.initialize()
        assert db_manager.connection is not None
        
        # Test basic query
        result = await db_manager.execute_query("SELECT 1")
        assert result is not None
        
    finally:
        await db_manager.close()


@pytest.mark.asyncio
async def test_cache_initialization():
    """Test cache manager initialization."""
    cache_manager = CacheManager()
    
    # Mock Redis for testing
    cache_manager.redis_pool = AsyncMock()
    cache_manager.redis_pool.ping = AsyncMock(return_value=True)
    cache_manager.redis_pool.set = AsyncMock(return_value=True)
    cache_manager.redis_pool.get = AsyncMock(return_value=b"test_value")
    
    try:
        await cache_manager.initialize()
        
        # Test basic operations
        await cache_manager.set("test_key", "test_value", ttl=60)
        result = await cache_manager.get("test_key")
        
        # Verify mock was called
        cache_manager.redis_pool.set.assert_called_once()
        cache_manager.redis_pool.get.assert_called_once()
        
    finally:
        await cache_manager.shutdown()


@pytest.mark.asyncio
async def test_metrics_collector_initialization():
    """Test metrics collector initialization."""
    metrics_collector = MetricsCollector()
    
    # Mock database for testing
    metrics_collector.db_manager = AsyncMock()
    
    try:
        await metrics_collector.start()
        
        # Test metric recording
        await metrics_collector.record_agent_execution(
            agent_name="test_agent",
            method_name="test_method",
            execution_time_ms=100.0,
            success=True
        )
        
        # Verify metrics are recorded
        agent_metrics = metrics_collector.get_agent_metrics()
        assert "test_agent" in agent_metrics
        assert agent_metrics["test_agent"].execution_count == 1
        
    finally:
        await metrics_collector.stop()


@pytest.mark.asyncio
async def test_agent_coordinator_initialization():
    """Test agent coordinator initialization."""
    # Create mock dependencies
    db_manager = AsyncMock()
    cache_manager = AsyncMock()
    metrics_collector = AsyncMock()
    
    coordinator = AgentCoordinator(
        db_manager=db_manager,
        cache_manager=cache_manager,
        metrics_collector=metrics_collector
    )
    
    try:
        # Mock agent initialization
        for agent_name in coordinator.agents:
            agent = coordinator.agents[agent_name]
            agent.initialize = AsyncMock()
            agent.health_check = AsyncMock(return_value=True)
            agent.shutdown = AsyncMock()
        
        await coordinator.initialize()
        
        # Verify all agents were initialized
        for agent in coordinator.agents.values():
            agent.initialize.assert_called_once()
        
        # Test health check
        health = await coordinator.health_check()
        assert health is True
        
    finally:
        await coordinator.shutdown()


def test_configuration_loading():
    """Test configuration loading and validation."""
    settings = get_settings()
    
    # Verify configuration structure
    assert hasattr(settings, 'database')
    assert hasattr(settings, 'cache')
    assert hasattr(settings, 'agents')
    assert hasattr(settings, 'api_keys')
    
    # Verify default values
    assert settings.database.duckdb_memory_limit == "1GB"
    assert settings.cache.default_ttl == 300
    assert settings.agents.discovery.max_tokens_per_source == 100


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
