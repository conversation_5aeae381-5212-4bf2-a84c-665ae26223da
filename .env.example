# Environment Configuration for Token Analyzer
# Copy this file to .env and fill in your API keys

# ==================== APPLICATION SETTINGS ====================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# ==================== BLOCKCHAIN APIs ====================
# Get from https://etherscan.io/apis
API_ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Get from https://infura.io/
API_INFURA_PROJECT_ID=your_infura_project_id_here

# Get from https://alchemy.com/
API_ALCHEMY_API_KEY=your_alchemy_api_key_here

# ==================== EXCHANGE APIs ====================
# Get from https://exchange.coinbase.com/
API_COINBASE_API_KEY=your_coinbase_api_key_here
API_COINBASE_API_SECRET=your_coinbase_api_secret_here
API_COINBASE_PASSPHRASE=your_coinbase_passphrase_here

# ==================== LLM APIs ====================
# Get from https://openrouter.ai/
API_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Get from https://console.anthropic.com/
API_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ==================== DATABASE SETTINGS ====================
# DuckDB
DB_DUCKDB_PATH=./data/analytics.db
DB_DUCKDB_MEMORY_LIMIT=2GB
DB_DUCKDB_THREADS=4

# Redis
DB_REDIS_URL=redis://localhost:6379
DB_REDIS_PASSWORD=
DB_REDIS_DB=0
DB_REDIS_MAX_CONNECTIONS=20

# Cache TTLs (in seconds)
DB_CACHE_TTL_DEFAULT=300
DB_CACHE_TTL_MARKET_DATA=60
DB_CACHE_TTL_CHAIN_DATA=3600

# ==================== VALIDATION CRITERIA ====================
VALIDATION_MIN_LIQUIDITY_USD=100000.0
VALIDATION_MIN_VOLUME_24H_USD=50000.0
VALIDATION_MIN_AGE_DAYS=7
VALIDATION_MAX_AGE_DAYS=365
VALIDATION_MIN_MARKET_CAP_USD=1000000.0
VALIDATION_MAX_VOLATILITY_24H=0.5
VALIDATION_MIN_FEAR_GREED_INDEX=20
VALIDATION_MIN_SOCIAL_MENTIONS=10
VALIDATION_REQUIRE_VERIFIED_CONTRACT=true
VALIDATION_REQUIRE_AUDIT=false

# ==================== SYSTEM SETTINGS ====================
# Application
APP_NAME=Token Analyzer
APP_VERSION=1.0.0

# MCP Server
MCP_SERVER_NAME=TokenAnalyzer
MCP_SERVER_PORT=8000
MCP_SERVER_HOST=0.0.0.0

# Processing
MAX_CONCURRENT_REQUESTS=10
MAX_WORKERS=4
BATCH_SIZE=50

# Scheduler
SCHEDULER_INTERVAL=900
ENABLE_SCHEDULER=true

# ==================== MONITORING SETTINGS ====================
MONITORING_ENABLE_METRICS=true
MONITORING_METRICS_PORT=9090
MONITORING_METRICS_ENDPOINT=/metrics
MONITORING_HEALTH_CHECK_INTERVAL=30
MONITORING_HEALTH_CHECK_TIMEOUT=5.0
MONITORING_ENABLE_ALERTS=false
MONITORING_ALERT_WEBHOOK_URL=
MONITORING_ENABLE_TRACING=false
MONITORING_JAEGER_ENDPOINT=

# ==================== API RATE LIMITS ====================
API_DEFAULT_RATE_LIMIT=100
API_BURST_RATE_LIMIT=200
API_REQUEST_TIMEOUT=30.0
API_CONNECTION_TIMEOUT=10.0
