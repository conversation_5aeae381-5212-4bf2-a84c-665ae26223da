[build-system]
requires = ["setuptools>=70.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "agentic-token-analyzer"
version = "1.0.0"
description = "Production-ready agentic system for token discovery and analysis using MCP"
readme = "README.md"
license = {text = "MIT"}
authors = [{name = "Token Analysis Team", email = "<EMAIL>"}]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Artificial Intelligence"
]
requires-python = ">=3.11"
dependencies = [
    # MCP Framework
    "mcp[cli]>=1.10.1",
    
    # Async HTTP and WebSocket
    "aiohttp[speedups]>=3.12.13",
    "aiofiles>=24.1.0",
    "websockets>=13.1",
    
    # Blockchain & Web3
    "web3>=7.12.0",
    "eth-account>=0.13.0",
    "eth-utils>=5.1.0",
    
    # Data Processing & Analysis
    "pandas>=2.2.3",
    "numpy>=2.0.0",
    "polars>=1.12.0",  # Modern DataFrame library
    "pyarrow>=18.1.0",
    
    # Technical Analysis
    "ta>=0.11.0",  # More modern than talib
    "yfinance>=0.2.48",
    
    # Database & Storage
    "duckdb>=1.1.3",
    "redis[hiredis]>=5.2.0",
    "sqlalchemy[asyncio]>=2.0.36",
    
    # Monitoring & Observability
    "structlog>=24.4.0",
    "prometheus-client>=0.21.0",
    "opentelemetry-api>=1.29.0",
    "opentelemetry-sdk>=1.29.0",
    
    # Security & Validation
    "pydantic>=2.10.4",
    "cryptography>=44.0.0",
    "python-jose[cryptography]>=3.3.0",
    
    # API & Rate Limiting
    "tenacity>=9.0.0",
    "limits>=3.13.0",
    "backoff>=2.2.1",
    
    # HTTP API Framework
    "fastapi>=0.115.6",
    "uvicorn[standard]>=0.32.1",
    
    # Configuration
    "pydantic-settings>=2.6.1",
    "python-dotenv>=1.0.1",
    
    # Caching
    "aiocache[redis,memcached]>=0.12.3",
    
    # Development
    "rich>=13.9.4",
    "typer>=0.15.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "black>=24.10.0",
    "ruff>=0.8.5",
    "mypy>=1.13.0",
    "pre-commit>=4.0.1",
]

test = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "httpx>=0.28.1",
    "respx>=0.21.1",
]

monitoring = [
    "grafana-client>=3.8.1",
    "elasticsearch>=8.17.0",
]

[project.scripts]
token-analyzer = "src.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
testpaths = ["tests"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
