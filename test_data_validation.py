#!/usr/bin/env python3
"""Test data validation with Great Expectations."""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta

sys.path.insert(0, 'src')

from utils.simple_validation import (
    SimpleDataValidator, validate_token_address, validate_price_data,
    validate_technical_indicators, ValidationResult
)
from utils.fetch_helpers import fetch_coingecko_data, discover_newest_tokens
from utils.technical_analysis import analyze_token_technicals

async def test_token_address_validation():
    """Test token address validation."""
    print("🔍 Testing Token Address Validation...")
    print("-" * 40)
    
    valid_addresses = [
        "******************************************",  # USDC
        "******************************************",  # WETH
        "******************************************"   # UNI
    ]
    
    invalid_addresses = [
        "0x123",  # Too short
        "0xGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG",  # Invalid chars
        "A0b86a33E6441E6C7D3b4c6d3b4c6d3b4c6d3b4c",   # Missing 0x
        None,
        ""
    ]
    
    passed = 0
    total = len(valid_addresses) + len(invalid_addresses)
    
    # Test valid addresses
    for addr in valid_addresses:
        if validate_token_address(addr):
            print(f"✅ Valid: {addr[:10]}...")
            passed += 1
        else:
            print(f"❌ Should be valid: {addr}")
    
    # Test invalid addresses
    for addr in invalid_addresses:
        if not validate_token_address(addr):
            print(f"✅ Invalid (correctly rejected): {addr}")
            passed += 1
        else:
            print(f"❌ Should be invalid: {addr}")
    
    success_rate = passed / total
    print(f"📊 Address validation: {passed}/{total} ({success_rate:.1%})")
    
    return success_rate >= 0.9

async def test_api_response_validation():
    """Test API response validation with real data."""
    print("\n📡 Testing API Response Validation...")
    print("-" * 40)
    
    try:
        # Test with CoinGecko data
        result = await fetch_coingecko_data(
            "simple/price",
            {"ids": "bitcoin,ethereum", "vs_currencies": "usd"}
        )
        
        if result.success:
            print(f"✅ Fetched CoinGecko data: {len(result.data)} tokens")
            
            # Validate the response
            validator = DataValidator()
            validation = validator.validate_api_response(
                result.data, "coingecko", "simple_price"
            )
            
            print(f"📊 Validation Result:")
            print(f"   Success: {validation.success}")
            print(f"   Data Points: {validation.data_points}")
            print(f"   Passed: {validation.passed_expectations}")
            print(f"   Failed: {validation.failed_expectations}")
            print(f"   Success Rate: {validation.success_rate:.1%}")
            
            if validation.errors:
                print(f"   Errors: {validation.errors[:3]}")
            
            return validation.success_rate >= 0.8
        else:
            print(f"❌ Failed to fetch CoinGecko data: {result.error}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def test_token_discovery_validation():
    """Test token discovery data validation."""
    print("\n🔍 Testing Token Discovery Validation...")
    print("-" * 40)
    
    try:
        # Get newest tokens
        result = await discover_newest_tokens(
            min_liquidity_usd=1000,
            max_age_hours=72,
            limit=5
        )
        
        if result.success and result.data["tokens"]:
            tokens = result.data["tokens"]
            print(f"✅ Discovered {len(tokens)} new tokens")
            
            # Validate token discovery data
            validator = DataValidator()
            validation = validator.validate_api_response(
                tokens, "dexscreener", "token_discovery"
            )
            
            print(f"📊 Token Discovery Validation:")
            print(f"   Success: {validation.success}")
            print(f"   Data Points: {validation.data_points}")
            print(f"   Success Rate: {validation.success_rate:.1%}")
            
            # Validate individual token addresses
            valid_addresses = 0
            for token in tokens:
                if validate_token_address(token.get("address")):
                    valid_addresses += 1
            
            address_rate = valid_addresses / len(tokens)
            print(f"   Valid Addresses: {valid_addresses}/{len(tokens)} ({address_rate:.1%})")
            
            return validation.success_rate >= 0.7 and address_rate >= 0.8
        else:
            print("⚠️ No tokens found for validation")
            return True  # Not a failure, just no data
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def test_technical_analysis_validation():
    """Test technical analysis output validation."""
    print("\n📊 Testing Technical Analysis Validation...")
    print("-" * 40)
    
    try:
        # Generate sample price data
        price_data = []
        base_price = 100.0
        
        for i in range(60):  # 60 data points
            timestamp = datetime.utcnow() - timedelta(hours=60-i)
            price = base_price * (1 + 0.01 * (i % 10 - 5))  # Oscillating price
            
            price_data.append({
                "timestamp": timestamp.isoformat(),
                "price": price,
                "volume": 10000 + i * 100
            })
        
        current_price = price_data[-1]["price"]
        
        # Perform technical analysis
        analysis = analyze_token_technicals(price_data, current_price)
        
        if analysis.get("success"):
            print(f"✅ Technical analysis completed")
            print(f"   Data Points: {analysis['data_points']}")
            print(f"   Overall Signal: {analysis['signals']['overall']}")
            
            # Validate technical indicators
            validation = validate_technical_indicators(analysis["indicators"])
            
            print(f"📊 Technical Indicators Validation:")
            print(f"   Success: {validation.success}")
            print(f"   Success Rate: {validation.success_rate:.1%}")
            
            if validation.errors:
                print(f"   Errors: {validation.errors[:3]}")
            
            return validation.success_rate >= 0.7
        else:
            print(f"❌ Technical analysis failed: {analysis.get('error')}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def test_price_data_validation():
    """Test price data validation."""
    print("\n💰 Testing Price Data Validation...")
    print("-" * 40)
    
    try:
        # Create sample price data
        valid_price_data = [
            {"timestamp": "2025-07-09T10:00:00Z", "price": 100.0, "volume": 10000},
            {"timestamp": "2025-07-09T11:00:00Z", "price": 101.5, "volume": 12000},
            {"timestamp": "2025-07-09T12:00:00Z", "price": 99.8, "volume": 11000}
        ]
        
        # Test valid data
        validation = validate_price_data(valid_price_data)
        
        print(f"📊 Valid Price Data Validation:")
        print(f"   Success: {validation.success}")
        print(f"   Data Points: {validation.data_points}")
        print(f"   Success Rate: {validation.success_rate:.1%}")
        
        # Create invalid price data
        invalid_price_data = [
            {"timestamp": "invalid", "price": -100.0, "volume": -1000},  # Invalid values
            {"timestamp": None, "price": None, "volume": None}  # Null values
        ]
        
        # Test invalid data (should fail validation)
        invalid_validation = validate_price_data(invalid_price_data)
        
        print(f"📊 Invalid Price Data Validation (should fail):")
        print(f"   Success: {invalid_validation.success}")
        print(f"   Errors: {len(invalid_validation.errors)}")
        
        # Success if valid data passes and invalid data fails
        return validation.success_rate >= 0.7 and not invalid_validation.success
        
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def main():
    """Run all data validation tests."""
    print("🚀 Data Validation Testing Suite")
    print("=" * 50)
    
    results = []
    
    # Test 1: Token address validation
    results.append(await test_token_address_validation())
    
    # Test 2: API response validation
    results.append(await test_api_response_validation())
    
    # Test 3: Token discovery validation
    results.append(await test_token_discovery_validation())
    
    # Test 4: Technical analysis validation
    results.append(await test_technical_analysis_validation())
    
    # Test 5: Price data validation
    results.append(await test_price_data_validation())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("📊 DATA VALIDATION TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total}")
    print(f"📈 Success Rate: {passed/total:.1%}")
    
    if passed == total:
        print("🎉 All data validation tests PASSED!")
        print("✅ Great Expectations integration is fully functional!")
    else:
        print("⚠️ Some tests failed - check implementation")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
